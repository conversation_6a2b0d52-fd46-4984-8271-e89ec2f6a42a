import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Institute from './institute.js'
import Module from './module.js'

export default class InstituteModuleAccess extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare instituteId: number

  @column()
  declare moduleId: number

  @column()
  declare isActive: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationships
   */
  @belongsTo(() => Institute)
  declare institute: BelongsTo<typeof Institute>

  @belongsTo(() => Module)
  declare module: BelongsTo<typeof Module>

  /**
   * Computed properties
   */
  get isEnabled() {
    return this.isActive
  }

  get isDisabled() {
    return !this.isActive
  }

  /**
   * Serialize institute module access data for API responses
   */
  serialize() {
    return {
      id: this.id,
      instituteId: this.instituteId,
      moduleId: this.moduleId,
      isActive: this.isActive,
      isEnabled: this.isEnabled,
      isDisabled: this.isDisabled,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
