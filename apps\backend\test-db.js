import mysql from 'mysql2/promise'

const dbConfig = {
  host: '127.0.0.1',
  port: 3307,
  user: 'root',
  password: '',
  database: 'lms'
}

async function testConnection() {
  try {
    console.log('Testing database connection...')
    const connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to MySQL database successfully!')
    
    // Test if tables exist
    const [tables] = await connection.execute('SHOW TABLES')
    console.log('📋 Available tables:', tables.map(t => Object.values(t)[0]))
    
    await connection.end()
    console.log('✅ Database test completed successfully!')
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
    process.exit(1)
  }
}

testConnection()
