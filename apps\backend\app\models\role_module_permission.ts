import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Role from './role.js'
import Module from './module.js'
import Permission from './permission.js'
import Institute from './institute.js'

export default class RoleModulePermission extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare roleId: number

  @column()
  declare moduleId: number

  @column()
  declare permissionId: number

  @column()
  declare instituteId: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationships
   */
  @belongsTo(() => Role)
  declare role: BelongsTo<typeof Role>

  @belongsTo(() => Module)
  declare module: BelongsTo<typeof Module>

  @belongsTo(() => Permission)
  declare permission: BelongsTo<typeof Permission>

  @belongsTo(() => Institute)
  declare institute: BelongsTo<typeof Institute>

  /**
   * Computed properties
   */
  get permissionKey() {
    return `${this.module?.name || 'unknown'}.${this.permission?.name || 'unknown'}`
  }

  /**
   * Serialize role module permission data for API responses
   */
  serialize() {
    return {
      id: this.id,
      roleId: this.roleId,
      moduleId: this.moduleId,
      permissionId: this.permissionId,
      instituteId: this.instituteId,
      permissionKey: this.permissionKey,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
