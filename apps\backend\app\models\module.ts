import { DateTime } from 'luxon'
import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm'
import type { HasMany } from '@adonisjs/lucid/types/relations'
import Permission from './permission.js'
import InstituteModuleAccess from './institute_module_access.js'
import RoleModulePermission from './role_module_permission.js'

export default class Module extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare displayName: string

  @column()
  declare description: string | null

  @column()
  declare isActive: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationships
   */
  @hasMany(() => Permission)
  declare permissions: HasMany<typeof Permission>

  @hasMany(() => InstituteModuleAccess)
  declare instituteAccess: HasMany<typeof InstituteModuleAccess>

  @hasMany(() => RoleModulePermission)
  declare rolePermissions: HasMany<typeof RoleModulePermission>

  /**
   * Computed properties
   */
  get isEnabled() {
    return this.isActive
  }

  /**
   * Serialize module data for API responses
   */
  serialize() {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      description: this.description,
      isActive: this.isActive,
      isEnabled: this.isEnabled,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
