{"version": 3, "file": "session_service.js", "sourceRoot": "", "sources": ["../../../app/services/session_service.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAA;AAEhC,OAAO,WAAW,MAAM,sBAAsB,CAAA;AA4B9C,MAAM,CAAC,OAAO,OAAO,cAAc;IAIjC,KAAK,CAAC,aAAa,CACjB,IAAU,EACV,GAAgB,EAChB,OAAe,EACf,SAA+B,EAC/B,SAAmB;QAEnB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;QAC9C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAA;QAGjE,MAAM,WAAW,CAAC,KAAK,EAAE;aACtB,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;aACxB,MAAM,CAAC,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,CAAA;QAEtC,MAAM,WAAW,GAAG;YAClB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACnC,OAAO;YACP,SAAS;YAGT,UAAU,EAAE,UAAU,CAAC,IAAI;YAC3B,UAAU,EAAE,UAAU,CAAC,IAAI;YAC3B,WAAW,EAAE,UAAU,CAAC,KAAK;YAC7B,YAAY,EAAE,UAAU,CAAC,MAAM;YAC/B,eAAe,EAAE,UAAU,CAAC,eAAe;YAC3C,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAG7B,WAAW,EAAE,WAAW,CAAC,IAAI;YAC7B,cAAc,EAAE,WAAW,CAAC,OAAO;YACnC,aAAa,EAAE,WAAW,CAAC,MAAM;YACjC,SAAS,EAAE,WAAW,CAAC,SAAS;YAGhC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE;YAC3B,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,MAAM,EAAE,YAAY,CAAC,MAAM;YAC3B,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,QAAQ,EAAE,YAAY,CAAC,QAAQ;YAC/B,GAAG,EAAE,YAAY,CAAC,GAAG;YAGrB,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE;YACvB,cAAc,EAAE,QAAQ,CAAC,GAAG,EAAE;YAC9B,SAAS;YAGT,MAAM,EAAE,QAAiB;YACzB,gBAAgB,EAAE,IAAI;YACtB,eAAe,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,CAAC;YAClF,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC;YAG5C,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI;YAC/C,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI;YACtC,QAAQ,EAAE;gBACR,WAAW,EAAE,UAAU;gBACvB,aAAa,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,IAAI;aAC9D;SACF,CAAA;QAED,OAAO,MAAM,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;IAC9C,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,SAAiB;QAC3C,MAAM,WAAW,CAAC,KAAK,EAAE;aACtB,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC;aAC7B,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;aACzB,MAAM,CAAC;YACN,cAAc,EAAE,QAAQ,CAAC,GAAG,EAAE;YAC9B,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE;SAC1B,CAAC,CAAA;IACN,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,MAAe;QACpD,MAAM,UAAU,GAAQ;YACtB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,QAAQ,CAAC,GAAG,EAAE;YACxB,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE;SAC1B,CAAA;QAED,IAAI,MAAM,EAAE,CAAC;YACX,UAAU,CAAC,aAAa,GAAG,MAAM,CAAA;QACnC,CAAC;QAED,MAAM,WAAW,CAAC,KAAK,EAAE;aACtB,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC;aAC7B,MAAM,CAAC,UAAU,CAAC,CAAA;IACvB,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,MAAc,EAAE,eAAwB;QAClE,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE;aAC9B,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;aACvB,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QAE5B,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;QAC9C,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC;YAChC,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,QAAQ,CAAC,GAAG,EAAE;YACxB,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE;SAC1B,CAAC,CAAA;QAEF,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACvB,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,OAAO,MAAM,WAAW,CAAC,KAAK,EAAE;aAC7B,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;aACvB,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;aACzB,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;aAC/C,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;IACtC,CAAC;IAKD,KAAK,CAAC,sBAAsB;QAC1B,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;aACrC,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;aAChD,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;aACzB,MAAM,CAAC;YACN,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE;SAC1B,CAAC,CAAA;QAEJ,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACvB,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,MAAc,EACd,GAAgB;QAEhB,MAAM,cAAc,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;aAC7C,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;aACvB,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;aAClE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;aAC1B,KAAK,CAAC,EAAE,CAAC,CAAA;QAEZ,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,CAAA;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;QAG5C,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;QAC/D,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,mCAAmC,EAAE,CAAA;QAC5E,CAAC;QAGD,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAA;QACnF,IAAI,eAAe,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;YAC1D,IAAI,YAAY,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,wBAAwB,EAAE,CAAA;YACjE,CAAC;QACH,CAAC;QAGD,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC,CAAA;QACrF,MAAM,aAAa,GAAG,GAAG,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,eAAe,EAAE,CAAA;QACxE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC3C,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,4BAA4B,EAAE,CAAA;QACrE,CAAC;QAED,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,CAAA;IAChC,CAAC;IAKO,eAAe,CAAC,GAAgB;QACtC,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;QAGxD,MAAM,QAAQ,GAAG,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC7D,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC9C,MAAM,SAAS,GAAG,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAA;QAExC,IAAI,UAAU,GAAG,SAAS,CAAA;QAC1B,IAAI,QAAQ;YAAE,UAAU,GAAG,QAAQ,CAAA;aAC9B,IAAI,QAAQ;YAAE,UAAU,GAAG,QAAQ,CAAA;aACnC,IAAI,SAAS;YAAE,UAAU,GAAG,SAAS,CAAA;QAG1C,IAAI,eAAe,GAAG,IAAI,CAAA;QAC1B,IAAI,SAAS,GAAG,IAAI,CAAA;QAEpB,IAAI,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5C,eAAe,GAAG,SAAS,CAAA;YAC3B,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;QACnE,CAAC;aAAM,IAAI,8BAA8B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1D,eAAe,GAAG,OAAO,CAAA;YACzB,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,8BAA8B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAA;QAC9F,CAAC;aAAM,IAAI,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAChD,eAAe,GAAG,SAAS,CAAA;YAC3B,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;QAChE,CAAC;aAAM,IAAI,+BAA+B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3D,eAAe,GAAG,KAAK,CAAA;YACvB,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAA;QAC/F,CAAC;QAED,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,IAAI;YACZ,eAAe;YACf,SAAS;YACT,QAAQ;SACT,CAAA;IACH,CAAC;IAKO,gBAAgB,CAAC,GAAgB;QACvC,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;QAExD,IAAI,WAAW,GAAG,IAAI,CAAA;QACtB,IAAI,cAAc,GAAG,IAAI,CAAA;QACzB,IAAI,aAAa,GAAG,IAAI,CAAA;QAGxB,IAAI,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACzC,WAAW,GAAG,QAAQ,CAAA;YACtB,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;YACnE,aAAa,GAAG,OAAO,CAAA;QACzB,CAAC;aAAM,IAAI,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACjD,WAAW,GAAG,SAAS,CAAA;YACvB,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;YACpE,aAAa,GAAG,OAAO,CAAA;QACzB,CAAC;aAAM,IAAI,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7E,WAAW,GAAG,QAAQ,CAAA;YACtB,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;YACpE,aAAa,GAAG,QAAQ,CAAA;QAC1B,CAAC;aAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,WAAW,GAAG,MAAM,CAAA;YACpB,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;YACjE,aAAa,GAAG,UAAU,CAAA;QAC5B,CAAC;QAED,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,cAAc;YACvB,MAAM,EAAE,aAAa;YACrB,SAAS;SACV,CAAA;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,SAAiB;QAO7C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAI;YACZ,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,IAAI;YACd,GAAG,EAAE,IAAI;SACV,CAAA;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,SAAiB,EAAE,UAAsB;QACrF,MAAM,qBAAqB,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;aACpD,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;aACvB,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC;aAC9B,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;QAEpE,OAAO,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC1C,OAAO,CAAC,SAAS,KAAK,SAAS;YAC/B,OAAO,CAAC,UAAU,KAAK,UAAU,CAAC,IAAI;YACtC,OAAO,CAAC,eAAe,KAAK,UAAU,CAAC,eAAe,CACvD,CAAA;IACH,CAAC;IAKO,SAAS,CAAC,SAAiB;QACjC,MAAM,WAAW,GAAG;YAClB,MAAM;YACN,UAAU;YACV,SAAS;YACT,UAAU;YACV,OAAO;YACP,OAAO;YACP,SAAS;YACT,OAAO;SACR,CAAA;QAED,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;IAC7D,CAAC;IAKO,iBAAiB;QACvB,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA;IACxE,CAAC;CACF"}