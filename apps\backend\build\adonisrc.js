import { defineConfig } from '@adonisjs/core/app';
export default defineConfig({
    commands: [
        () => import('@adonisjs/core/commands'),
        () => import('@adonisjs/lucid/commands'),
        () => import('@adonisjs/auth/commands'),
        () => import('@adonisjs/bouncer/commands'),
        () => import('@adonisjs/mail/commands'),
    ],
    providers: [
        () => import('@adonisjs/core/providers/app_provider'),
        () => import('@adonisjs/core/providers/hash_provider'),
        () => import('@adonisjs/core/providers/repl_provider'),
        () => import('@adonisjs/core/providers/vinejs_provider'),
        () => import('@adonisjs/core/providers/edge_provider'),
        () => import('@adonisjs/session/session_provider'),
        () => import('@adonisjs/shield/shield_provider'),
        () => import('@adonisjs/static/static_provider'),
        () => import('@adonisjs/cors/cors_provider'),
        () => import('@adonisjs/lucid/database_provider'),
        () => import('@adonisjs/auth/auth_provider'),
        () => import('@adonisjs/bouncer/bouncer_provider'),
        () => import('@adonisjs/limiter/limiter_provider'),
        () => import('@adonisjs/redis/redis_provider'),
        () => import('@adonisjs/mail/mail_provider'),
        () => import('@adonisjs/drive/drive_provider'),
    ],
    preloads: [
        () => import('./start/routes.js'),
        () => import('./start/kernel.js'),
    ],
    tests: {
        suites: [
            {
                files: ['tests/unit/**/*.spec(.ts|.js)'],
                name: 'unit',
                timeout: 2000,
            },
            {
                files: ['tests/functional/**/*.spec(.ts|.js)'],
                name: 'functional',
                timeout: 30000,
            },
        ],
        forceExit: false,
    },
    metaFiles: [
        {
            pattern: 'resources/views/**/*.edge',
            reloadServer: false,
        },
        {
            pattern: 'public/**',
            reloadServer: false,
        },
    ],
    hooks: {
        onBuildStarting: [() => import('@adonisjs/assembler/hooks')],
    },
    directories: {
        config: 'config',
        public: 'public',
        contracts: 'contracts',
        providers: 'providers',
        database: 'database',
        migrations: 'database/migrations',
        seeders: 'database/seeders',
        factories: 'database/factories',
        views: 'resources/views',
        start: 'start',
        tmp: 'tmp',
        tests: 'tests',
    },
});
//# sourceMappingURL=adonisrc.js.map