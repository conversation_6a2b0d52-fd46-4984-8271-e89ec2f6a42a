'use client'

import { useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { initializeAuth, getCurrentUser } from '@/store/slices/authSlice'
import authService from '@/services/authService'

export default function AuthInitializer({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch()
  const { isAuthenticated, user } = useAppSelector((state) => state.auth)

  useEffect(() => {
    // Initialize auth state from localStorage
    dispatch(initializeAuth())

    // If we have tokens but no user data, fetch current user
    if (authService.isAuthenticated() && !user) {
      dispatch(getCurrentUser())
    }
  }, [dispatch, user])

  return <>{children}</>
}
