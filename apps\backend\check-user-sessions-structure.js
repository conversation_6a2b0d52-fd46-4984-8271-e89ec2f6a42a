import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function checkUserSessionsStructure() {
  let connection

  try {
    console.log('🔍 Checking user_sessions table structure...')
    
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Get table structure
    const [columns] = await connection.execute('DESCRIBE user_sessions')
    
    console.log('\n📋 user_sessions table columns:')
    columns.forEach((col, index) => {
      console.log(`${index + 1}. ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`)
    })

    console.log(`\n📊 Total columns: ${columns.length}`)

  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

checkUserSessionsStructure()
