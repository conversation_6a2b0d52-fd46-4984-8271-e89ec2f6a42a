import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'permissions';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.string('name', 100).notNullable().unique();
            table.string('display_name', 100).notNullable();
            table.string('description', 255).nullable();
            table.integer('module_id').unsigned().notNullable();
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.foreign('module_id').references('id').inTable('modules').onDelete('CASCADE');
            table.index(['module_id']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=008_create_permissions_table.js.map