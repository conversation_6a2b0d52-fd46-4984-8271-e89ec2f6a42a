import { DateTime } from 'luxon'
import { HttpContext } from '@adonisjs/core/http'
import UserSession from '#models/user_session'
import User from '#models/user'

interface DeviceInfo {
  type: string | null
  name: string | null
  model: string | null
  vendor: string | null
  operatingSystem: string | null
  osVersion: string | null
  isMobile: boolean
}

interface BrowserInfo {
  name: string | null
  version: string | null
  engine: string | null
  userAgent: string
}

interface LocationInfo {
  country: string | null
  region: string | null
  city: string | null
  timezone: string | null
  isp: string | null
}

export default class SessionService {
  /**
   * Create a new user session
   */
  async createSession(
    user: User,
    ctx: HttpContext,
    tokenId: string,
    tokenType: 'access' | 'refresh',
    expiresAt: DateTime
  ): Promise<UserSession> {
    const deviceInfo = this.parseDeviceInfo(ctx)
    const browserInfo = this.parseBrowserInfo(ctx)
    const locationInfo = await this.getLocationInfo(ctx.request.ip())
    
    // Mark all other sessions as not current
    await UserSession.query()
      .where('userId', user.id)
      .update({ isCurrentSession: false })

    const sessionData = {
      userId: user.id,
      sessionId: this.generateSessionId(),
      tokenId,
      tokenType,
      
      // Device info
      deviceType: deviceInfo.type,
      deviceName: deviceInfo.name,
      deviceModel: deviceInfo.model,
      deviceVendor: deviceInfo.vendor,
      operatingSystem: deviceInfo.operatingSystem,
      osVersion: deviceInfo.osVersion,
      isMobile: deviceInfo.isMobile,
      
      // Browser info
      browserName: browserInfo.name,
      browserVersion: browserInfo.version,
      browserEngine: browserInfo.engine,
      userAgent: browserInfo.userAgent,
      
      // Network info
      ipAddress: ctx.request.ip(),
      country: locationInfo.country,
      region: locationInfo.region,
      city: locationInfo.city,
      timezone: locationInfo.timezone,
      isp: locationInfo.isp,
      
      // Session details
      loginAt: DateTime.now(),
      lastActivityAt: DateTime.now(),
      expiresAt,
      
      // Status
      status: 'active' as const,
      isCurrentSession: true,
      isTrustedDevice: await this.isTrustedDevice(user.id, ctx.request.ip(), deviceInfo),
      isBot: this.detectBot(browserInfo.userAgent),
      
      // Additional data
      referrer: ctx.request.header('referer') || null,
      landingPage: ctx.request.url() || null,
      metadata: {
        loginMethod: 'password', // Could be 'oauth', 'sso', etc.
        clientVersion: ctx.request.header('x-client-version') || null,
      }
    }

    return await UserSession.create(sessionData)
  }

  /**
   * Update session activity
   */
  async updateSessionActivity(sessionId: string): Promise<void> {
    await UserSession.query()
      .where('sessionId', sessionId)
      .where('status', 'active')
      .update({
        lastActivityAt: DateTime.now(),
        updatedAt: DateTime.now(),
      })
  }

  /**
   * Revoke a session
   */
  async revokeSession(sessionId: string, reason?: string): Promise<void> {
    const updateData: any = {
      status: 'revoked',
      logoutAt: DateTime.now(),
      updatedAt: DateTime.now(),
    }

    if (reason) {
      updateData.securityNotes = reason
    }

    await UserSession.query()
      .where('sessionId', sessionId)
      .update(updateData)
  }

  /**
   * Revoke all user sessions except current
   */
  async revokeAllUserSessions(userId: number, exceptSessionId?: string): Promise<number> {
    const query = UserSession.query()
      .where('userId', userId)
      .where('status', 'active')

    if (exceptSessionId) {
      query.whereNot('sessionId', exceptSessionId)
    }

    const result = await query.update({
      status: 'revoked',
      logoutAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    return result[0] || 0
  }

  /**
   * Get user's active sessions
   */
  async getUserActiveSessions(userId: number): Promise<UserSession[]> {
    return await UserSession.query()
      .where('userId', userId)
      .where('status', 'active')
      .where('expiresAt', '>', DateTime.now().toSQL())
      .orderBy('lastActivityAt', 'desc')
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    const result = await UserSession.query()
      .where('expiresAt', '<=', DateTime.now().toSQL())
      .where('status', 'active')
      .update({
        status: 'expired',
        updatedAt: DateTime.now(),
      })

    return result[0] || 0
  }

  /**
   * Detect suspicious login activity
   */
  async detectSuspiciousActivity(
    userId: number,
    ctx: HttpContext
  ): Promise<{ isSuspicious: boolean; reason?: string }> {
    const recentSessions = await UserSession.query()
      .where('userId', userId)
      .where('loginAt', '>', DateTime.now().minus({ hours: 24 }).toSQL())
      .orderBy('loginAt', 'desc')
      .limit(10)

    const currentIp = ctx.request.ip()
    const deviceInfo = this.parseDeviceInfo(ctx)

    // Check for multiple IPs in short time
    const uniqueIps = new Set(recentSessions.map(s => s.ipAddress))
    if (uniqueIps.size > 5) {
      return { isSuspicious: true, reason: 'Multiple IP addresses in 24 hours' }
    }

    // Check for new country
    const recentCountries = new Set(recentSessions.map(s => s.country).filter(Boolean))
    if (recentCountries.size > 0) {
      const locationInfo = await this.getLocationInfo(currentIp)
      if (locationInfo.country && !recentCountries.has(locationInfo.country)) {
        return { isSuspicious: true, reason: 'Login from new country' }
      }
    }

    // Check for unusual device
    const recentDevices = recentSessions.map(s => `${s.deviceType}-${s.operatingSystem}`)
    const currentDevice = `${deviceInfo.type}-${deviceInfo.operatingSystem}`
    if (!recentDevices.includes(currentDevice)) {
      return { isSuspicious: true, reason: 'Login from new device type' }
    }

    return { isSuspicious: false }
  }

  /**
   * Parse device information from user agent
   */
  private parseDeviceInfo(ctx: HttpContext): DeviceInfo {
    const userAgent = ctx.request.header('user-agent') || ''
    
    // Simple device detection (in production, use a library like ua-parser-js)
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent)
    const isTablet = /iPad|Tablet/.test(userAgent)
    const isDesktop = !isMobile && !isTablet

    let deviceType = 'unknown'
    if (isMobile) deviceType = 'mobile'
    else if (isTablet) deviceType = 'tablet'
    else if (isDesktop) deviceType = 'desktop'

    // Extract OS info
    let operatingSystem = null
    let osVersion = null
    
    if (/Windows NT (\d+\.\d+)/.test(userAgent)) {
      operatingSystem = 'Windows'
      osVersion = userAgent.match(/Windows NT (\d+\.\d+)/)?.[1] || null
    } else if (/Mac OS X (\d+[._]\d+[._]\d+)/.test(userAgent)) {
      operatingSystem = 'macOS'
      osVersion = userAgent.match(/Mac OS X (\d+[._]\d+[._]\d+)/)?.[1]?.replace(/_/g, '.') || null
    } else if (/Android (\d+\.\d+)/.test(userAgent)) {
      operatingSystem = 'Android'
      osVersion = userAgent.match(/Android (\d+\.\d+)/)?.[1] || null
    } else if (/iPhone OS (\d+[._]\d+[._]\d+)/.test(userAgent)) {
      operatingSystem = 'iOS'
      osVersion = userAgent.match(/iPhone OS (\d+[._]\d+[._]\d+)/)?.[1]?.replace(/_/g, '.') || null
    }

    return {
      type: deviceType,
      name: null, // Would need more sophisticated parsing
      model: null,
      vendor: null,
      operatingSystem,
      osVersion,
      isMobile,
    }
  }

  /**
   * Parse browser information from user agent
   */
  private parseBrowserInfo(ctx: HttpContext): BrowserInfo {
    const userAgent = ctx.request.header('user-agent') || ''
    
    let browserName = null
    let browserVersion = null
    let browserEngine = null

    // Simple browser detection
    if (/Chrome\/(\d+\.\d+)/.test(userAgent)) {
      browserName = 'Chrome'
      browserVersion = userAgent.match(/Chrome\/(\d+\.\d+)/)?.[1] || null
      browserEngine = 'Blink'
    } else if (/Firefox\/(\d+\.\d+)/.test(userAgent)) {
      browserName = 'Firefox'
      browserVersion = userAgent.match(/Firefox\/(\d+\.\d+)/)?.[1] || null
      browserEngine = 'Gecko'
    } else if (/Safari\/(\d+\.\d+)/.test(userAgent) && !/Chrome/.test(userAgent)) {
      browserName = 'Safari'
      browserVersion = userAgent.match(/Version\/(\d+\.\d+)/)?.[1] || null
      browserEngine = 'WebKit'
    } else if (/Edge\/(\d+\.\d+)/.test(userAgent)) {
      browserName = 'Edge'
      browserVersion = userAgent.match(/Edge\/(\d+\.\d+)/)?.[1] || null
      browserEngine = 'EdgeHTML'
    }

    return {
      name: browserName,
      version: browserVersion,
      engine: browserEngine,
      userAgent,
    }
  }

  /**
   * Get location information from IP address
   */
  private async getLocationInfo(ipAddress: string): Promise<LocationInfo> {
    // In production, integrate with a geolocation service like:
    // - MaxMind GeoIP2
    // - IPinfo.io
    // - ipapi.co
    
    // For now, return mock data
    return {
      country: null,
      region: null,
      city: null,
      timezone: null,
      isp: null,
    }
  }

  /**
   * Check if device is trusted
   */
  private async isTrustedDevice(userId: number, ipAddress: string, deviceInfo: DeviceInfo): Promise<boolean> {
    const recentTrustedSessions = await UserSession.query()
      .where('userId', userId)
      .where('isTrustedDevice', true)
      .where('loginAt', '>', DateTime.now().minus({ days: 30 }).toSQL())

    return recentTrustedSessions.some(session => 
      session.ipAddress === ipAddress &&
      session.deviceType === deviceInfo.type &&
      session.operatingSystem === deviceInfo.operatingSystem
    )
  }

  /**
   * Detect if user agent is a bot
   */
  private detectBot(userAgent: string): boolean {
    const botPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
    ]

    return botPatterns.some(pattern => pattern.test(userAgent))
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}
