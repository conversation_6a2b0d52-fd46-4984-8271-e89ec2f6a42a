"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth-login/register/page",{

/***/ "(app-pages-browser)/./src/app/auth-login/register/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/auth-login/register/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InstituteRegisterPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/store */ \"(app-pages-browser)/./src/store/index.ts\");\n/* harmony import */ var _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/slices/authSlice */ \"(app-pages-browser)/./src/store/slices/authSlice.ts\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Building2,CheckCircle,Eye,EyeOff,Globe,GraduationCap,Loader2,Shield,TrendingUp,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction InstituteRegisterPage() {\n    _s();\n    const dispatch = (0,_store__WEBPACK_IMPORTED_MODULE_8__.useAppDispatch)();\n    const { isLoading, error, isAuthenticated } = (0,_store__WEBPACK_IMPORTED_MODULE_8__.useAppSelector)((state)=>state.auth);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        instituteName: \"\",\n        firstName: \"\",\n        lastName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated) {\n            router.push(\"/institute-admin\");\n        }\n    }, [\n        isAuthenticated,\n        router\n    ]);\n    // Clear error when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_9__.clearError)());\n    }, [\n        dispatch\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const validateStep1 = ()=>{\n        const { instituteName, firstName, lastName } = formData;\n        return instituteName && firstName && lastName;\n    };\n    const validateStep2 = ()=>{\n        const { email, password, confirmPassword } = formData;\n        return email && password && confirmPassword && password === confirmPassword && password.length >= 8;\n    };\n    const handleNextStep = ()=>{\n        if (validateStep1()) {\n            setCurrentStep(2);\n            dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_9__.clearError)());\n        }\n    };\n    const handlePrevStep = ()=>{\n        setCurrentStep(1);\n        dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_9__.clearError)());\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (currentStep === 1) {\n            handleNextStep();\n            return;\n        }\n        if (!validateStep2()) {\n            return;\n        }\n        try {\n            const result = await dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_9__.registerInstitute)(formData));\n            if (_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_9__.registerInstitute.fulfilled.match(result)) {\n                // Registration successful, redirect to dashboard\n                router.push(\"/institute-admin\");\n            }\n        } catch (err) {\n            // Error is handled by Redux\n            console.error(\"Registration failed:\", err);\n        }\n    };\n    const features = [\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Course Management\",\n            description: \"Create and manage comprehensive courses with multimedia content\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Student Analytics\",\n            description: \"Track student progress and performance with detailed analytics\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Certification System\",\n            description: \"Issue digital certificates and badges for course completion\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Performance Insights\",\n            description: \"Get insights into learning patterns and institutional metrics\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"Secure Platform\",\n            description: \"Enterprise-grade security with data protection compliance\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            title: \"Easy Integration\",\n            description: \"Seamlessly integrate with existing systems and tools\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/10\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 flex flex-col justify-center px-8 py-8 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold mb-3 leading-tight\",\n                                            children: \"Transform Your Educational Institution\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-blue-100 mb-6 leading-relaxed\",\n                                            children: \"Join thousands of institutions worldwide using our comprehensive learning management system.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 gap-3 mb-6\",\n                                    children: features.slice(0, 4).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"h-5 w-5 text-blue-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-white text-sm\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-blue-100\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"10K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Institutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"1M+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Students\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"50K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Courses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full lg:w-1/2 flex items-center justify-center p-4 lg:p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:hidden inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-slate-900 dark:text-white mb-1\",\n                                        children: currentStep === 1 ? \"Institute Details\" : \"Account Setup\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                        children: [\n                                            \"Step \",\n                                            currentStep,\n                                            \" of 2: \",\n                                            currentStep === 1 ? \"Basic Information\" : \"Login Credentials\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm dark:bg-slate-800/80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg text-sm flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    error\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 21\n                                            }, this),\n                                            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"instituteName\",\n                                                                className: \"text-xs font-medium text-slate-700 dark:text-slate-300\",\n                                                                children: \"Institute Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"instituteName\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Harvard University\",\n                                                                        value: formData.instituteName,\n                                                                        onChange: (e)=>handleInputChange(\"instituteName\", e.target.value),\n                                                                        required: true,\n                                                                        className: \"pl-9 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"firstName\",\n                                                                className: \"text-xs font-medium text-slate-700 dark:text-slate-300\",\n                                                                children: \"First Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"firstName\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"John\",\n                                                                        value: formData.firstName,\n                                                                        onChange: (e)=>handleInputChange(\"firstName\", e.target.value),\n                                                                        required: true,\n                                                                        className: \"pl-9 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"lastName\",\n                                                                className: \"text-xs font-medium text-slate-700 dark:text-slate-300\",\n                                                                children: \"Last Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"lastName\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Doe\",\n                                                                        value: formData.lastName,\n                                                                        onChange: (e)=>handleInputChange(\"lastName\", e.target.value),\n                                                                        required: true,\n                                                                        className: \"pl-9 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true),\n                                            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"email\",\n                                                                className: \"text-xs font-medium text-slate-700 dark:text-slate-300\",\n                                                                children: \"Email Address *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"email\",\n                                                                        type: \"email\",\n                                                                        placeholder: \"<EMAIL>\",\n                                                                        value: formData.email,\n                                                                        onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                                        required: true,\n                                                                        className: \"pl-9 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"password\",\n                                                                className: \"text-xs font-medium text-slate-700 dark:text-slate-300\",\n                                                                children: \"Password *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"password\",\n                                                                        type: showPassword ? \"text\" : \"password\",\n                                                                        placeholder: \"Enter password (min. 8 characters)\",\n                                                                        value: formData.password,\n                                                                        onChange: (e)=>handleInputChange(\"password\", e.target.value),\n                                                                        required: true,\n                                                                        className: \"pl-9 pr-10 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600\",\n                                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 45\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 78\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"confirmPassword\",\n                                                                className: \"text-xs font-medium text-slate-700 dark:text-slate-300\",\n                                                                children: \"Confirm Password *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"confirmPassword\",\n                                                                        type: showConfirmPassword ? \"text\" : \"password\",\n                                                                        placeholder: \"Confirm your password\",\n                                                                        value: formData.confirmPassword,\n                                                                        onChange: (e)=>handleInputChange(\"confirmPassword\", e.target.value),\n                                                                        required: true,\n                                                                        className: \"pl-9 pr-10 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600\",\n                                                                        children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 52\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 85\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-red-600 dark:text-red-400 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Passwords do not match\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        onClick: handlePrevStep,\n                                                        className: \"flex-1 h-10 text-sm\",\n                                                        children: \"Back\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: isLoading || (currentStep === 1 ? !validateStep1() : !validateStep2()),\n                                                        className: \"\".concat(currentStep === 2 ? \"flex-1\" : \"w-full\", \" h-10 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium text-sm rounded-lg shadow-lg hover:shadow-xl transition-all duration-200\"),\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Creating Account...\"\n                                                            ]\n                                                        }, void 0, true) : currentStep === 1 ? \"Next Step\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Building2_CheckCircle_Eye_EyeOff_Globe_GraduationCap_Loader2_Shield_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Register\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center pt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-slate-600 dark:text-slate-400\",\n                                                    children: [\n                                                        \"Already have an account?\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/auth-login/login\",\n                                                            className: \"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 transition-colors\",\n                                                            children: \"Sign in here\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\app\\\\auth-login\\\\register\\\\page.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(InstituteRegisterPage, \"cODwQvnX+Z5RohmCmk07WpVr9Uk=\", false, function() {\n    return [\n        _store__WEBPACK_IMPORTED_MODULE_8__.useAppDispatch,\n        _store__WEBPACK_IMPORTED_MODULE_8__.useAppSelector,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = InstituteRegisterPage;\nvar _c;\n$RefreshReg$(_c, \"InstituteRegisterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth-login/register/page.tsx\n"));

/***/ })

});