var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm';
import User from './user.js';
import Branch from './branch.js';
import Course from './course.js';
import InstituteModuleAccess from './institute_module_access.js';
import RoleModulePermission from './role_module_permission.js';
import UserRole from './user_role.js';
export default class Institute extends BaseModel {
    get isActive() {
        return this.status === 'active';
    }
    get isSubscriptionActive() {
        return this.subscriptionStatus === 'active' || this.subscriptionStatus === 'trial';
    }
    get isSubscriptionExpired() {
        if (!this.subscriptionExpiresAt)
            return false;
        return this.subscriptionExpiresAt < DateTime.now();
    }
    get domain() {
        if (this.customDomain)
            return this.customDomain;
        if (this.subdomain)
            return `${this.subdomain}.lms-platform.com`;
        return null;
    }
    serialize() {
        return {
            id: this.id,
            name: this.name,
            code: this.code,
            slug: this.slug,
            description: this.description,
            logo: this.logo,
            banner: this.banner,
            website: this.website,
            email: this.email,
            phone: this.phone,
            address: this.address,
            city: this.city,
            state: this.state,
            country: this.country,
            zipCode: this.zipCode,
            timezone: this.timezone,
            currency: this.currency,
            language: this.language,
            status: this.status,
            subscriptionPlan: this.subscriptionPlan,
            subscriptionStatus: this.subscriptionStatus,
            subscriptionExpiresAt: this.subscriptionExpiresAt,
            maxStudents: this.maxStudents,
            maxStaff: this.maxStaff,
            maxCourses: this.maxCourses,
            customDomain: this.customDomain,
            subdomain: this.subdomain,
            domain: this.domain,
            theme: this.theme,
            settings: this.settings,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], Institute.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "name", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "code", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "slug", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "description", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "logo", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "banner", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "website", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "email", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "phone", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "address", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "city", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "state", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "country", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "zipCode", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "timezone", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "currency", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "language", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "status", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "subscriptionPlan", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Institute.prototype, "subscriptionStatus", void 0);
__decorate([
    column.dateTime(),
    __metadata("design:type", Object)
], Institute.prototype, "subscriptionExpiresAt", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Institute.prototype, "maxStudents", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Institute.prototype, "maxStaff", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Institute.prototype, "maxCourses", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "customDomain", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "subdomain", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "theme", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Institute.prototype, "settings", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], Institute.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], Institute.prototype, "updatedAt", void 0);
__decorate([
    column.dateTime(),
    __metadata("design:type", Object)
], Institute.prototype, "deletedAt", void 0);
__decorate([
    hasMany(() => User),
    __metadata("design:type", Object)
], Institute.prototype, "users", void 0);
__decorate([
    hasMany(() => Branch),
    __metadata("design:type", Object)
], Institute.prototype, "branches", void 0);
__decorate([
    hasMany(() => Course),
    __metadata("design:type", Object)
], Institute.prototype, "courses", void 0);
__decorate([
    hasMany(() => InstituteModuleAccess),
    __metadata("design:type", Object)
], Institute.prototype, "moduleAccess", void 0);
__decorate([
    hasMany(() => RoleModulePermission),
    __metadata("design:type", Object)
], Institute.prototype, "rolePermissions", void 0);
__decorate([
    hasMany(() => UserRole),
    __metadata("design:type", Object)
], Institute.prototype, "userRoles", void 0);
//# sourceMappingURL=institute.js.map