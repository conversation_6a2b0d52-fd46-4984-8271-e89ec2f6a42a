import { BaseSeeder } from '@adonisjs/lucid/seeders'
import UserRole from '#models/user_role'
import User from '#models/user'
import Role from '#models/role'
import Institute from '#models/institute'

export default class extends BaseSeeder {
  async run() {
    // Get institutes
    const demoInstitute = await Institute.findByOrFail('slug', 'demo-institute')
    const harvard = await Institute.findByOrFail('slug', 'harvard-university')
    const mit = await Institute.findByOrFail('slug', 'mit')
    
    // Get roles
    const superAdminRole = await Role.findByOrFail('name', 'super_admin')
    const lmsAdminRole = await Role.findByOrFail('name', 'lms_admin')
    const instituteAdminRole = await Role.findByOrFail('name', 'institute_admin')
    const instituteStaffRole = await Role.findByOrFail('name', 'institute_staff')
    const instructorRole = await Role.findByOrFail('name', 'instructor')
    const studentRole = await Role.findByOrFail('name', 'student')
    
    // Get users
    const superAdmin = await User.findByOrFail('email', '<EMAIL>')
    const lmsAdmin = await User.findByOrFail('email', '<EMAIL>')
    
    // Institute admins
    const demoAdmin = await User.findByOrFail('email', '<EMAIL>')
    const harvardAdmin = await User.findByOrFail('email', '<EMAIL>')
    const mitAdmin = await User.findByOrFail('email', '<EMAIL>')
    
    // Institute staff
    const demoStaff = await User.findByOrFail('email', '<EMAIL>')
    const demoInstructor = await User.findByOrFail('email', '<EMAIL>')
    
    // Students
    const johnDoe = await User.findByOrFail('email', '<EMAIL>')
    const janeSmith = await User.findByOrFail('email', '<EMAIL>')
    const bobJohnson = await User.findByOrFail('email', '<EMAIL>')
    const aliceWilson = await User.findByOrFail('email', '<EMAIL>')
    const charlieBrown = await User.findByOrFail('email', '<EMAIL>')
    
    // Assign roles to users
    await UserRole.createMany([
      // System admins don't need institute_id
      {
        user_id: superAdmin.id,
        role_id: superAdminRole.id,
        institute_id: null
      },
      {
        user_id: lmsAdmin.id,
        role_id: lmsAdminRole.id,
        institute_id: null
      },
      
      // Institute admins
      {
        user_id: demoAdmin.id,
        role_id: instituteAdminRole.id,
        institute_id: demoInstitute.id
      },
      {
        user_id: harvardAdmin.id,
        role_id: instituteAdminRole.id,
        institute_id: harvard.id
      },
      {
        user_id: mitAdmin.id,
        role_id: instituteAdminRole.id,
        institute_id: mit.id
      },
      
      // Institute staff
      {
        user_id: demoStaff.id,
        role_id: instituteStaffRole.id,
        institute_id: demoInstitute.id
      },
      {
        user_id: demoInstructor.id,
        role_id: instructorRole.id,
        institute_id: demoInstitute.id
      },
      
      // Students
      {
        user_id: johnDoe.id,
        role_id: studentRole.id,
        institute_id: demoInstitute.id
      },
      {
        user_id: janeSmith.id,
        role_id: studentRole.id,
        institute_id: demoInstitute.id
      },
      {
        user_id: bobJohnson.id,
        role_id: studentRole.id,
        institute_id: demoInstitute.id
      },
      {
        user_id: aliceWilson.id,
        role_id: studentRole.id,
        institute_id: harvard.id
      },
      {
        user_id: charlieBrown.id,
        role_id: studentRole.id,
        institute_id: mit.id
      }
    ])
  }
}