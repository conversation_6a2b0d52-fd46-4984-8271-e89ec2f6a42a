{"version": 3, "file": "010_create_role_module_permissions_table.js", "sourceRoot": "", "sources": ["../../../database/migrations/010_create_role_module_permissions_table.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAA;AAEnD,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IAC3B,SAAS,GAAG,yBAAyB,CAAA;IAE/C,KAAK,CAAC,EAAE;QACN,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAChD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAA;YAChC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAA;YACjD,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAA;YACnD,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAA;YACvD,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAA;YAGtD,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YACpE,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YAGpE,KAAK,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,CAAC,EAAE,8BAA8B,CAAC,CAAA;YAGvG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAC9E,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAClF,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAC1F,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAGxF,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;YACxB,KAAK,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;YAC1B,KAAK,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,CAAA;YAC9B,KAAK,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,CAAC,CAAA;QAC/B,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACvC,CAAC;CACF"}