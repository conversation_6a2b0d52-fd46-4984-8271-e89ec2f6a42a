import router from '@adonisjs/core/services/router';
import server from '@adonisjs/core/services/server';
server.errorHandler(() => import('#exceptions/handler'));
server.use([
    () => import('@adonisjs/static/static_middleware'),
    () => import('@adonisjs/cors/cors_middleware'),
    () => import('#middleware/container_bindings_middleware'),
    () => import('#middleware/force_json_response_middleware'),
]);
router.use([
    () => import('@adonisjs/core/bodyparser_middleware'),
    () => import('@adonisjs/session/session_middleware'),
    () => import('@adonisjs/shield/shield_middleware'),
    () => import('@adonisjs/auth/initialize_auth_middleware'),
]);
export const middleware = router.named({
    auth: () => import('@adonisjs/auth/auth_middleware'),
    guest: () => import('#middleware/guest_middleware'),
    throttle: () => import('@adonisjs/limiter/throttle_middleware'),
    role: () => import('#middleware/role_middleware'),
    institute: () => import('#middleware/institute_middleware'),
});
//# sourceMappingURL=kernel.js.map