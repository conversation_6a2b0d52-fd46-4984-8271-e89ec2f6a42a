var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm';
import Institute from './institute.js';
import User from './user.js';
import Enrollment from './enrollment.js';
export default class Course extends BaseModel {
    get isPublished() {
        return this.status === 'published';
    }
    get hasDiscount() {
        return this.discountPrice !== null && this.discountPrice < this.price;
    }
    get finalPrice() {
        return this.hasDiscount ? this.discountPrice : this.price;
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], Course.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Course.prototype, "instituteId", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Course.prototype, "instructorId", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Course.prototype, "title", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Course.prototype, "slug", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "description", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "shortDescription", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "thumbnail", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Course.prototype, "price", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "discountPrice", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Course.prototype, "currency", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "duration", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Course.prototype, "level", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "category", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "tags", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Course.prototype, "status", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "maxStudents", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "startDate", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "endDate", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "requirements", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Course.prototype, "learningOutcomes", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], Course.prototype, "isFeatured", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], Course.prototype, "isPublic", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], Course.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], Course.prototype, "updatedAt", void 0);
__decorate([
    belongsTo(() => Institute),
    __metadata("design:type", Object)
], Course.prototype, "institute", void 0);
__decorate([
    belongsTo(() => User, {
        foreignKey: 'instructorId',
    }),
    __metadata("design:type", Object)
], Course.prototype, "instructor", void 0);
__decorate([
    hasMany(() => Enrollment),
    __metadata("design:type", Object)
], Course.prototype, "enrollments", void 0);
//# sourceMappingURL=course.js.map