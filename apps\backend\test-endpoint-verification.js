async function testEndpointVerification() {
  try {
    console.log('🔍 Testing endpoint verification...')
    
    // Test root endpoint
    console.log('\n1. Testing root endpoint...')
    const rootResponse = await fetch('http://localhost:3333/')
    const rootResult = await rootResponse.json()
    console.log('✅ Root response:', rootResult.message)
    
    // Test health endpoint
    console.log('\n2. Testing health endpoint...')
    const healthResponse = await fetch('http://localhost:3333/api/health')
    const healthResult = await healthResponse.json()
    console.log('✅ Health response:', healthResult.status)
    
    // Test login endpoint with wrong credentials to see if it hits
    console.log('\n3. Testing login endpoint with wrong credentials...')
    const loginResponse = await fetch('http://localhost:3333/api/v1/auth/institute/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    })
    
    const loginResult = await loginResponse.json()
    console.log('✅ Login response status:', loginResponse.status)
    console.log('✅ Login response:', loginResult)
    
    // Test with correct credentials
    console.log('\n4. Testing login endpoint with correct credentials...')
    const correctLoginResponse = await fetch('http://localhost:3333/api/v1/auth/institute/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    })
    
    const correctLoginResult = await correctLoginResponse.json()
    console.log('✅ Correct login response status:', correctLoginResponse.status)
    console.log('✅ Correct login response:', correctLoginResult.success ? 'SUCCESS' : 'FAILED')
    
    if (correctLoginResult.success) {
      console.log('👤 User:', correctLoginResult.data.user.firstName, correctLoginResult.data.user.lastName)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testEndpointVerification()
