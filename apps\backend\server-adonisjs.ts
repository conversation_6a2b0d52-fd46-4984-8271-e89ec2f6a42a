/*
|--------------------------------------------------------------------------
| AdonisJS HTTP server entrypoint
|--------------------------------------------------------------------------
|
| This is a simplified AdonisJS server setup that works with the current
| configuration and provides the institute registration endpoint.
|
*/

import 'reflect-metadata'
import { Application } from '@adonisjs/core/app'
import { Ignitor } from '@adonisjs/core'

/**
 * URL to the application root
 */
const APP_ROOT = new URL('../', import.meta.url)

/**
 * The importer function
 */
const IMPORTER = (filePath: string) => {
  if (filePath.startsWith('./') || filePath.startsWith('../')) {
    return import(new URL(filePath, APP_ROOT).href)
  }
  return import(filePath)
}

try {
  console.log('🚀 Starting AdonisJS server...')
  
  const ignitor = new Ignitor(APP_ROOT, { importer: IMPORTER })
  const app = ignitor.createApp('web')
  
  await app.init()
  await app.boot()
  
  const server = await app.container.make('server')
  const router = await app.container.make('router')
  
  // Basic middleware
  server.use([
    async (ctx, next) => {
      ctx.response.header('Access-Control-Allow-Origin', '*')
      ctx.response.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE')
      ctx.response.header('Access-Control-Allow-Headers', 'Content-Type,Authorization')
      
      if (ctx.request.method() === 'OPTIONS') {
        ctx.response.status(200)
        return
      }
      
      await next()
    }
  ])
  
  // Basic routes
  router.get('/', async ({ response }) => {
    return response.json({
      message: 'LMS Backend API is running with AdonisJS!',
      version: '1.0.0',
      timestamp: new Date().toISOString()
    })
  })
  
  router.get('/api/health', async ({ response }) => {
    return response.json({
      status: 'healthy',
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    })
  })
  
  // Institute registration endpoint
  router.post('/api/v1/auth/institute/register', async ({ request, response }) => {
    try {
      const body = request.body()
      const { instituteName, firstName, lastName, email, password, confirmPassword } = body

      // Basic validation
      if (!instituteName || !firstName || !lastName || !email || !password || !confirmPassword) {
        return response.status(400).json({
          success: false,
          message: 'All fields are required',
          errors: {
            instituteName: !instituteName ? ['Institute name is required'] : undefined,
            firstName: !firstName ? ['First name is required'] : undefined,
            lastName: !lastName ? ['Last name is required'] : undefined,
            email: !email ? ['Email is required'] : undefined,
            password: !password ? ['Password is required'] : undefined,
            confirmPassword: !confirmPassword ? ['Confirm password is required'] : undefined,
          }
        })
      }

      if (password !== confirmPassword) {
        return response.status(400).json({
          success: false,
          message: 'Passwords do not match',
          errors: {
            confirmPassword: ['Passwords do not match']
          }
        })
      }

      if (password.length < 8) {
        return response.status(400).json({
          success: false,
          message: 'Password must be at least 8 characters long',
          errors: {
            password: ['Password must be at least 8 characters long']
          }
        })
      }

      // For now, just return success without database operations
      // We'll add database operations once the basic server is working
      return response.status(201).json({
        success: true,
        message: 'Institute registration request received successfully. Database integration pending.',
        data: {
          institute: {
            name: instituteName,
            email: email,
            status: 'pending'
          },
          user: {
            firstName: firstName,
            lastName: lastName,
            email: email,
            role: 'institute_admin',
            status: 'pending'
          }
        }
      })

    } catch (error) {
      console.error('Institute registration error:', error)
      return response.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      })
    }
  })
  
  // Institute login endpoint
  router.post('/api/v1/auth/institute/login', async ({ request, response }) => {
    try {
      const body = request.body()
      const { email, password } = body

      if (!email || !password) {
        return response.status(400).json({
          success: false,
          message: 'Email and password are required'
        })
      }

      // For now, just return a mock response
      // We'll add real authentication once database is working
      return response.json({
        success: true,
        message: 'Login endpoint working. Authentication pending database integration.',
        data: {
          user: {
            email: email,
            role: 'institute_admin',
            status: 'active'
          },
          tokens: {
            accessToken: 'mock-token',
            tokenType: 'Bearer',
            expiresIn: '24h'
          }
        }
      })

    } catch (error) {
      console.error('Login error:', error)
      return response.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      })
    }
  })
  
  // Start the server
  const PORT = process.env.PORT || 3333
  const HOST = process.env.HOST || 'localhost'
  
  await app.listen(HOST, Number(PORT))
  
  console.log(`✅ AdonisJS server is running on http://${HOST}:${PORT}`)
  console.log(`📚 API Documentation: http://${HOST}:${PORT}/`)
  console.log(`🏥 Health Check: http://${HOST}:${PORT}/api/health`)
  
} catch (error) {
  console.error('❌ Failed to start AdonisJS server:', error)
  process.exit(1)
}
