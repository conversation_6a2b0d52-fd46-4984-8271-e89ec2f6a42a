import vine from '@vinejs/vine';
export const loginValidator = vine.compile(vine.object({
    email: vine
        .string()
        .email()
        .normalizeEmail()
        .maxLength(255),
    password: vine
        .string()
        .minLength(6)
        .maxLength(255),
    remember: vine
        .boolean()
        .optional()
}));
export const instituteRegisterValidator = vine.compile(vine.object({
    instituteName: vine
        .string()
        .trim()
        .minLength(2)
        .maxLength(255),
    firstName: vine
        .string()
        .trim()
        .minLength(2)
        .maxLength(50),
    lastName: vine
        .string()
        .trim()
        .minLength(2)
        .maxLength(50),
    email: vine
        .string()
        .email()
        .normalizeEmail()
        .maxLength(255),
    password: vine
        .string()
        .minLength(8)
        .maxLength(255)
        .confirmed(),
    confirmPassword: vine
        .string()
}));
export const instituteLoginValidator = vine.compile(vine.object({
    email: vine
        .string()
        .email()
        .normalizeEmail()
        .maxLength(255),
    password: vine
        .string()
        .minLength(6)
        .maxLength(255),
    remember: vine
        .boolean()
        .optional()
}));
export const studentLoginValidator = vine.compile(vine.object({
    instituteCode: vine
        .string()
        .trim()
        .toUpperCase()
        .minLength(3)
        .maxLength(20)
        .regex(/^[A-Z0-9]+$/),
    studentId: vine
        .string()
        .trim()
        .toUpperCase()
        .minLength(3)
        .maxLength(50)
        .regex(/^[A-Z0-9]+$/),
    password: vine
        .string()
        .minLength(6)
        .maxLength(255),
    remember: vine
        .boolean()
        .optional()
}));
export const refreshTokenValidator = vine.compile(vine.object({
    refreshToken: vine
        .string()
        .trim()
        .minLength(10)
}));
export const forgotPasswordValidator = vine.compile(vine.object({
    email: vine
        .string()
        .email()
        .normalizeEmail()
        .maxLength(255),
    instituteCode: vine
        .string()
        .trim()
        .toUpperCase()
        .minLength(3)
        .maxLength(20)
        .regex(/^[A-Z0-9]+$/)
        .optional()
}));
export const resetPasswordValidator = vine.compile(vine.object({
    token: vine
        .string()
        .trim()
        .minLength(10),
    password: vine
        .string()
        .minLength(8)
        .maxLength(255)
        .confirmed(),
    password_confirmation: vine
        .string()
}));
export const changePasswordValidator = vine.compile(vine.object({
    currentPassword: vine
        .string()
        .minLength(6)
        .maxLength(255),
    password: vine
        .string()
        .minLength(8)
        .maxLength(255)
        .confirmed(),
    password_confirmation: vine
        .string()
}));
export const updateProfileValidator = vine.compile(vine.object({
    firstName: vine
        .string()
        .trim()
        .minLength(2)
        .maxLength(50),
    lastName: vine
        .string()
        .trim()
        .minLength(2)
        .maxLength(50),
    phone: vine
        .string()
        .trim()
        .minLength(10)
        .maxLength(20)
        .regex(/^[\+]?[0-9\s\-\(\)]+$/)
        .optional(),
    avatar: vine
        .file({
        size: '2mb',
        extnames: ['jpg', 'jpeg', 'png', 'gif', 'webp']
    })
        .optional(),
    preferences: vine
        .object({})
        .optional()
}));
//# sourceMappingURL=auth_validator.js.map