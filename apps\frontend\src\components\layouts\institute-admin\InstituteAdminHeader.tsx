"use client"

import { useRouter } from 'next/navigation'
import { useAppDispatch, useAppSelector } from '@/store'
import { logoutUser } from '@/store/slices/authSlice'
import { useApiToast } from '@/hooks/use-api-toast'
import { BaseHeader } from '../shared/headers'

interface InstituteAdminHeaderProps {
  onMenuToggle: () => void
}

export function InstituteAdminHeader({ onMenuToggle }: InstituteAdminHeaderProps) {
  const dispatch = useAppDispatch()
  const router = useRouter()
  const { user: authUser } = useAppSelector((state) => state.auth)
  const { showLogoutSuccess, handleApiError } = useApiToast()

  // Use real user data from auth state or fallback
  const user = authUser ? {
    name: `${authUser.first_name || ''} ${authUser.last_name || ''}`.trim() || authUser.email,
    email: authUser.email,
    role: authUser.user_type === 'institute_admin' ? 'Institute Admin' :
          authUser.user_type === 'institute_staff' ? 'Institute Staff' :
          authUser.user_type || 'User',
    avatar: undefined
  } : {
    name: 'Institute Admin',
    email: '<EMAIL>',
    role: 'Institute Admin',
    avatar: undefined
  }

  const handleLogout = async () => {
    try {
      const result = await dispatch(logoutUser())

      if (logoutUser.fulfilled.match(result)) {
        // Show success toast
        showLogoutSuccess()

        // Redirect to login page after a short delay
        setTimeout(() => {
          router.push('/auth-login/login')
        }, 1500)
      } else {
        // Handle logout failure
        const errorMessage = result.payload as string || 'Logout failed. Please try again.'
        handleApiError({ message: errorMessage }, 'Logout failed')
      }
    } catch (err: any) {
      // Handle unexpected errors
      handleApiError(err, 'Logout failed. Please try again.')
    }
  }

  const notifications = [
    {
      id: '1',
      title: 'New Student Registration',
      message: '5 new students have registered for Computer Science',
      time: '10 minutes ago',
      read: false,
      type: 'info' as const
    },
    {
      id: '2',
      title: 'Course Update Required',
      message: 'Mathematics 101 needs curriculum review',
      time: '2 hours ago',
      read: false,
      type: 'warning' as const
    }
  ]

  return (
    <BaseHeader
      onMenuToggle={onMenuToggle}
      user={user}
      notifications={notifications}
      onLogout={handleLogout}
      className="border-b border-green-200 dark:border-green-800"
    />
  )
}
