"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/components/layouts/institute-admin/InstituteAdminHeader.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/layouts/institute-admin/InstituteAdminHeader.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstituteAdminHeader: function() { return /* binding */ InstituteAdminHeader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store */ \"(app-pages-browser)/./src/store/index.ts\");\n/* harmony import */ var _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/slices/authSlice */ \"(app-pages-browser)/./src/store/slices/authSlice.ts\");\n/* harmony import */ var _hooks_use_api_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-api-toast */ \"(app-pages-browser)/./src/hooks/use-api-toast.ts\");\n/* harmony import */ var _shared_headers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/headers */ \"(app-pages-browser)/./src/components/layouts/shared/headers/index.ts\");\n/* __next_internal_client_entry_do_not_use__ InstituteAdminHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction InstituteAdminHeader(param) {\n    let { onMenuToggle } = param;\n    _s();\n    const dispatch = (0,_store__WEBPACK_IMPORTED_MODULE_2__.useAppDispatch)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user: authUser } = (0,_store__WEBPACK_IMPORTED_MODULE_2__.useAppSelector)((state)=>state.auth);\n    const { showLogoutSuccess } = (0,_hooks_use_api_toast__WEBPACK_IMPORTED_MODULE_4__.useApiToast)();\n    // Use real user data from auth state or fallback\n    const user = authUser ? {\n        name: \"\".concat(authUser.first_name || \"\", \" \").concat(authUser.last_name || \"\").trim() || authUser.email,\n        email: authUser.email,\n        role: authUser.user_type === \"institute_admin\" ? \"Institute Admin\" : authUser.user_type === \"institute_staff\" ? \"Institute Staff\" : authUser.user_type || \"User\",\n        avatar: undefined\n    } : {\n        name: \"Institute Admin\",\n        email: \"<EMAIL>\",\n        role: \"Institute Admin\",\n        avatar: undefined\n    };\n    const handleLogout = async ()=>{\n        try {\n            await dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_3__.logoutUser)());\n            // Always show success toast and redirect since tokens are cleared regardless of API response\n            showLogoutSuccess();\n            // Redirect to login page after a short delay\n            setTimeout(()=>{\n                router.push(\"/auth-login/login\");\n            }, 1500);\n        } catch (err) {\n            // Even if there's an error, still redirect since tokens are cleared\n            showLogoutSuccess();\n            setTimeout(()=>{\n                router.push(\"/auth-login/login\");\n            }, 1500);\n        }\n    };\n    const notifications = [\n        {\n            id: \"1\",\n            title: \"New Student Registration\",\n            message: \"5 new students have registered for Computer Science\",\n            time: \"10 minutes ago\",\n            read: false,\n            type: \"info\"\n        },\n        {\n            id: \"2\",\n            title: \"Course Update Required\",\n            message: \"Mathematics 101 needs curriculum review\",\n            time: \"2 hours ago\",\n            read: false,\n            type: \"warning\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_headers__WEBPACK_IMPORTED_MODULE_5__.BaseHeader, {\n        onMenuToggle: onMenuToggle,\n        user: user,\n        notifications: notifications,\n        onLogout: handleLogout,\n        className: \"border-b border-green-200 dark:border-green-800\"\n    }, void 0, false, {\n        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\components\\\\layouts\\\\institute-admin\\\\InstituteAdminHeader.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_s(InstituteAdminHeader, \"hcg0p3JvlQu3Qii6aY8RfTw9TkM=\", false, function() {\n    return [\n        _store__WEBPACK_IMPORTED_MODULE_2__.useAppDispatch,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _store__WEBPACK_IMPORTED_MODULE_2__.useAppSelector,\n        _hooks_use_api_toast__WEBPACK_IMPORTED_MODULE_4__.useApiToast\n    ];\n});\n_c = InstituteAdminHeader;\nvar _c;\n$RefreshReg$(_c, \"InstituteAdminHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layouts/institute-admin/InstituteAdminHeader.tsx\n"));

/***/ })

});