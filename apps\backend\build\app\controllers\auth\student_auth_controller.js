import User from '#models/user';
import Institute from '#models/institute';
import { studentLoginValidator, refreshTokenValidator } from '#validators/auth_validator';
import StudentAuthService from '#services/auth/student_auth_service';
export default class StudentAuthController {
    authService = new StudentAuthService();
    async login({ request, response }) {
        try {
            const payload = await request.validateUsing(studentLoginValidator);
            const institute = await Institute.findBy('code', payload.instituteCode);
            if (!institute) {
                return response.status(401).json({
                    success: false,
                    message: 'Invalid institute code',
                    errors: {
                        instituteCode: ['Institute not found']
                    }
                });
            }
            if (institute.status !== 'active') {
                return response.status(403).json({
                    success: false,
                    message: 'Institute is not active',
                    errors: {
                        institute: ['Institute has been suspended or deactivated']
                    }
                });
            }
            const user = await User.query()
                .where('student_id', payload.studentId)
                .where('institute_id', institute.id)
                .where('role', 'student')
                .first();
            if (!user) {
                return response.status(401).json({
                    success: false,
                    message: 'Invalid credentials',
                    errors: {
                        studentId: ['Invalid student ID or password']
                    }
                });
            }
            const isPasswordValid = await user.verifyPassword(payload.password);
            if (!isPasswordValid) {
                return response.status(401).json({
                    success: false,
                    message: 'Invalid credentials',
                    errors: {
                        password: ['Invalid student ID or password']
                    }
                });
            }
            if (user.status !== 'active') {
                return response.status(403).json({
                    success: false,
                    message: 'Account is not active',
                    errors: {
                        status: ['Your account has been suspended or deactivated']
                    }
                });
            }
            await user.load('institute');
            await user.load('branch');
            await user.load('enrollments', (query) => {
                query.preload('course');
            });
            const tokens = await this.authService.generateTokens(user);
            await this.authService.updateLastLogin(user, request.ip());
            return response.status(200).json({
                success: true,
                message: 'Login successful',
                data: {
                    user: user.serialize(),
                    institute: institute.serialize(),
                    enrollments: user.enrollments.map(enrollment => ({
                        ...enrollment.serialize(),
                        course: enrollment.course.serialize()
                    })),
                    tokens,
                    permissions: await this.authService.getUserPermissions(user)
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async me({ auth, response }) {
        try {
            const user = auth.user;
            await user.load('institute');
            await user.load('branch');
            await user.load('enrollments', (query) => {
                query.preload('course');
            });
            return response.status(200).json({
                success: true,
                data: {
                    user: user.serialize(),
                    institute: user.institute?.serialize(),
                    enrollments: user.enrollments.map(enrollment => ({
                        ...enrollment.serialize(),
                        course: enrollment.course.serialize()
                    })),
                    permissions: await this.authService.getUserPermissions(user)
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async dashboard({ auth, response }) {
        try {
            const user = auth.user;
            const dashboardData = await this.authService.getDashboardData(user);
            return response.status(200).json({
                success: true,
                data: dashboardData
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async refresh({ request, response }) {
        try {
            const payload = await request.validateUsing(refreshTokenValidator);
            const tokens = await this.authService.refreshTokens(payload.refreshToken);
            return response.status(200).json({
                success: true,
                message: 'Token refreshed successfully',
                data: { tokens }
            });
        }
        catch (error) {
            return response.status(401).json({
                success: false,
                message: 'Invalid refresh token',
                error: error.message
            });
        }
    }
    async logout({ auth, response }) {
        try {
            const user = auth.user;
            await this.authService.logout(user);
            return response.status(200).json({
                success: true,
                message: 'Logged out successfully'
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
}
//# sourceMappingURL=student_auth_controller.js.map