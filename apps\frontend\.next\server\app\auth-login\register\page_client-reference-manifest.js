globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth-login/register/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/auth-login/register/page.tsx":{"*":{"id":"(ssr)/./src/app/auth-login/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme-provider.tsx":{"*":{"id":"(ssr)/./src/components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(ssr)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/store/providers.tsx":{"*":{"id":"(ssr)/./src/store/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\wamp64\\www\\projects\\lms_lte\\apps\\frontend\\src\\app\\auth-login\\register\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth-login/register/page.tsx","name":"*","chunks":["app/auth-login/register/page","static/chunks/app/auth-login/register/page.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\apps\\frontend\\src\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./src/components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\apps\\frontend\\src\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./src/components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\apps\\frontend\\src\\store\\providers.tsx":{"id":"(app-pages-browser)/./src/store/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Lato\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"700\",\"900\"],\"variable\":\"--font-lato\"}],\"variableName\":\"lato\"}":{"id":"(app-pages-browser)/../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Lato\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"700\",\"900\"],\"variable\":\"--font-lato\"}],\"variableName\":\"lato\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Merriweather\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"700\",\"900\"],\"variable\":\"--font-merriweather\"}],\"variableName\":\"merriweather\"}":{"id":"(app-pages-browser)/../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Merriweather\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"700\",\"900\"],\"variable\":\"--font-merriweather\"}],\"variableName\":\"merriweather\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\apps\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\wamp64\\www\\projects\\lms_lte\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"C:\\wamp64\\www\\projects\\lms_lte\\apps\\frontend\\src\\":[],"C:\\wamp64\\www\\projects\\lms_lte\\apps\\frontend\\src\\app\\layout":["static/css/app/layout.css"],"C:\\wamp64\\www\\projects\\lms_lte\\apps\\frontend\\src\\app\\auth-login\\register\\page":[]}}