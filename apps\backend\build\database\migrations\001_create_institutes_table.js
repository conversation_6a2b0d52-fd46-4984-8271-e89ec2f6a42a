import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'institutes';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.string('name', 255).notNullable();
            table.string('code', 20).notNullable().unique();
            table.string('slug', 255).notNullable().unique();
            table.text('description').nullable();
            table.string('logo', 500).nullable();
            table.string('banner', 500).nullable();
            table.string('website', 500).nullable();
            table.string('email', 255).notNullable();
            table.string('phone', 20).nullable();
            table.text('address').nullable();
            table.string('city', 100).nullable();
            table.string('state', 100).nullable();
            table.string('country', 100).nullable();
            table.string('zip_code', 20).nullable();
            table.string('timezone', 50).defaultTo('UTC');
            table.string('currency', 3).defaultTo('USD');
            table.string('language', 5).defaultTo('en');
            table.enum('status', ['active', 'inactive', 'suspended', 'pending']).defaultTo('pending');
            table.enum('subscription_plan', ['basic', 'professional', 'enterprise']).defaultTo('basic');
            table.enum('subscription_status', ['active', 'inactive', 'trial', 'expired']).defaultTo('trial');
            table.timestamp('subscription_expires_at').nullable();
            table.integer('max_students').defaultTo(100);
            table.integer('max_staff').defaultTo(10);
            table.integer('max_courses').defaultTo(50);
            table.string('custom_domain', 255).nullable().unique();
            table.string('subdomain', 100).nullable().unique();
            table.json('theme').nullable();
            table.json('settings').nullable();
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('deleted_at', { useTz: true }).nullable();
            table.index(['status']);
            table.index(['subscription_status']);
            table.index(['created_at']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=001_create_institutes_table.js.map