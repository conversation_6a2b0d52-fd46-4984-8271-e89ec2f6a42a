import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function checkTables() {
  let connection

  try {
    console.log('🔍 Checking database tables...')

    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Get all tables
    const [tables] = await connection.execute('SHOW TABLES')
    console.log(`\n📋 Found ${tables.length} tables in database:`)

    tables.forEach((table, index) => {
      const tableName = Object.values(table)[0]
      console.log(`${index + 1}. ${tableName}`)
    })

    // Get detailed table information
    console.log('\n📊 Table Details:')
    for (const table of tables) {
      const tableName = Object.values(table)[0]

      // Get column count
      const [columns] = await connection.execute(`DESCRIBE ${tableName}`)

      // Get row count
      const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`)
      const rowCount = rows[0].count

      console.log(`\n🔹 ${tableName}:`)
      console.log(`   Columns: ${columns.length}`)
      console.log(`   Rows: ${rowCount}`)

      // Show first few column names
      const columnNames = columns.slice(0, 5).map(col => col.Field).join(', ')
      console.log(`   Sample columns: ${columnNames}${columns.length > 5 ? '...' : ''}`)
    }

    // Check for specific LMS tables
    const expectedTables = [
      'institutes', 'users', 'branches', 'courses', 'enrollments',
      'modules', 'roles', 'permissions', 'institute_module_access',
      'role_module_permissions', 'user_roles', 'user_sessions'
    ]

    console.log('\n🎯 Expected LMS Tables Check:')
    const existingTableNames = tables.map(table => Object.values(table)[0])

    expectedTables.forEach(expectedTable => {
      const exists = existingTableNames.includes(expectedTable)
      console.log(`${exists ? '✅' : '❌'} ${expectedTable}`)
    })

    const missingTables = expectedTables.filter(table => !existingTableNames.includes(table))
    const extraTables = existingTableNames.filter(table => !expectedTables.includes(table))

    if (missingTables.length > 0) {
      console.log(`\n⚠️  Missing tables: ${missingTables.join(', ')}`)
    }

    if (extraTables.length > 0) {
      console.log(`\n📝 Additional tables: ${extraTables.join(', ')}`)
    }

    console.log(`\n📈 Summary:`)
    console.log(`   Total tables: ${tables.length}`)
    console.log(`   Expected tables: ${expectedTables.length}`)
    console.log(`   Missing: ${missingTables.length}`)
    console.log(`   Extra: ${extraTables.length}`)

  } catch (error) {
    console.error('❌ Error checking tables:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

checkTables()
