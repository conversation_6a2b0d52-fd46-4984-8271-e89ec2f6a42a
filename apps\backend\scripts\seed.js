import mysql from 'mysql2/promise'
import bcrypt from 'bcryptjs'
import { config } from 'dotenv'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function seedDatabase() {
  let connection

  try {
    console.log('🌱 Seeding database...')
    
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Hash passwords
    const hashedPassword = await bcrypt.hash('admin123', 12)
    const studentPassword = await bcrypt.hash('student123', 12)

    // Insert sample institutes
    console.log('📚 Seeding institutes...')
    await connection.execute(`
      INSERT IGNORE INTO institutes (
        name, code, slug, email, status, subscription_plan, subscription_status,
        timezone, currency, language, max_students, max_staff, max_courses
      ) VALUES 
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'Demo University', 'DEMO_UNIV', 'demo-university', '<EMAIL>', 
      'active', 'premium', 'active', 'UTC', 'USD', 'en', 1000, 50, 100,
      
      'Test College', 'TEST_COL', 'test-college', '<EMAIL>', 
      'active', 'basic', 'active', 'UTC', 'USD', 'en', 500, 25, 50
    ])

    // Get institute IDs
    const [institutes] = await connection.execute('SELECT id, name FROM institutes ORDER BY id')
    console.log(`✅ ${institutes.length} institutes seeded`)

    // Insert sample users
    console.log('👥 Seeding users...')
    await connection.execute(`
      INSERT IGNORE INTO users (
        institute_id, first_name, last_name, email, password, role, status, email_verified_at
      ) VALUES 
      (?, ?, ?, ?, ?, ?, ?, NOW()),
      (?, ?, ?, ?, ?, ?, ?, NOW()),
      (?, ?, ?, ?, ?, ?, ?, NOW()),
      (?, ?, ?, ?, ?, ?, ?, NOW()),
      (?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      // Super Admin
      1, 'Super', 'Admin', '<EMAIL>', hashedPassword, 'super_admin', 'active',
      
      // Demo University Admin
      1, 'John', 'Doe', '<EMAIL>', hashedPassword, 'institute_admin', 'active',
      
      // Demo University Staff
      1, 'Jane', 'Smith', '<EMAIL>', hashedPassword, 'institute_staff', 'active',
      
      // Demo University Student
      1, 'Alice', 'Johnson', '<EMAIL>', studentPassword, 'student', 'active',
      
      // Test College Admin
      2, 'Bob', 'Wilson', '<EMAIL>', hashedPassword, 'institute_admin', 'active'
    ])

    // Get user count
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users')
    console.log(`✅ ${userCount[0].count} users seeded`)

    // Insert sample branches
    console.log('🏢 Seeding branches...')
    await connection.execute(`
      INSERT IGNORE INTO branches (
        institute_id, name, code, description, address, city, state, country, phone, email
      ) VALUES 
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      1, 'Main Campus', 'MAIN', 'Primary campus with all facilities', 
      '123 University Ave', 'Cambridge', 'MA', 'USA', '******-555-0100', '<EMAIL>',
      
      1, 'Science Campus', 'SCI', 'Dedicated science and research facilities', 
      '456 Research Blvd', 'Cambridge', 'MA', 'USA', '******-555-0200', '<EMAIL>',
      
      2, 'Downtown Campus', 'DOWN', 'Urban campus in city center', 
      '789 College St', 'Boston', 'MA', 'USA', '******-555-0300', '<EMAIL>'
    ])

    // Get branch count
    const [branchCount] = await connection.execute('SELECT COUNT(*) as count FROM branches')
    console.log(`✅ ${branchCount[0].count} branches seeded`)

    // Insert sample courses
    console.log('📖 Seeding courses...')
    const [instructors] = await connection.execute(
      'SELECT id FROM users WHERE role IN ("institute_admin", "institute_staff") LIMIT 2'
    )

    if (instructors.length > 0) {
      await connection.execute(`
        INSERT IGNORE INTO courses (
          institute_id, instructor_id, title, slug, description, short_description,
          price, level, status, max_students, language
        ) VALUES 
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        1, instructors[0].id, 'Introduction to Computer Science', 'intro-computer-science',
        'A comprehensive introduction to computer science fundamentals including programming, algorithms, and data structures.',
        'Learn the basics of computer science and programming', 299.99, 'beginner', 'published', 50, 'en',
        
        1, instructors[0].id, 'Advanced Mathematics', 'advanced-mathematics',
        'Advanced mathematical concepts including calculus, linear algebra, and discrete mathematics.',
        'Master advanced mathematical concepts', 399.99, 'advanced', 'published', 30, 'en',
        
        1, instructors[1] ? instructors[1].id : instructors[0].id, 'Web Development Fundamentals', 'web-dev-fundamentals',
        'Learn HTML, CSS, JavaScript and modern web development practices.',
        'Build modern web applications', 499.99, 'intermediate', 'published', 40, 'en',
        
        2, instructors[0].id, 'Business Administration Basics', 'business-admin-basics',
        'Introduction to business administration, management principles, and organizational behavior.',
        'Learn business fundamentals', 199.99, 'beginner', 'published', 60, 'en'
      ])

      // Get course count
      const [courseCount] = await connection.execute('SELECT COUNT(*) as count FROM courses')
      console.log(`✅ ${courseCount[0].count} courses seeded`)

      // Insert sample enrollments
      console.log('📝 Seeding enrollments...')
      const [students] = await connection.execute('SELECT id FROM users WHERE role = "student"')
      const [courses] = await connection.execute('SELECT id FROM courses LIMIT 3')

      if (students.length > 0 && courses.length > 0) {
        await connection.execute(`
          INSERT IGNORE INTO enrollments (
            user_id, course_id, status, progress
          ) VALUES 
          (?, ?, ?, ?),
          (?, ?, ?, ?),
          (?, ?, ?, ?)
        `, [
          students[0].id, courses[0].id, 'enrolled', 75.50,
          students[0].id, courses[1] ? courses[1].id : courses[0].id, 'enrolled', 45.25,
          students[0].id, courses[2] ? courses[2].id : courses[0].id, 'completed', 100.00
        ])

        const [enrollmentCount] = await connection.execute('SELECT COUNT(*) as count FROM enrollments')
        console.log(`✅ ${enrollmentCount[0].count} enrollments seeded`)
      }
    }

    console.log('🎉 Database seeding completed successfully!')
    console.log('')
    console.log('📋 Sample Login Credentials:')
    console.log('┌─────────────────────────────────────────────────────────────┐')
    console.log('│ Super Admin:                                                │')
    console.log('│   Email: <EMAIL>                        │')
    console.log('│   Password: admin123                                        │')
    console.log('│                                                             │')
    console.log('│ Institute Admin (Demo University):                         │')
    console.log('│   Email: <EMAIL>                          │')
    console.log('│   Password: admin123                                        │')
    console.log('│                                                             │')
    console.log('│ Institute Staff:                                            │')
    console.log('│   Email: <EMAIL>                     │')
    console.log('│   Password: admin123                                        │')
    console.log('│                                                             │')
    console.log('│ Student:                                                    │')
    console.log('│   Email: <EMAIL>         │')
    console.log('│   Password: student123                                      │')
    console.log('└─────────────────────────────────────────────────────────────┘')

  } catch (error) {
    console.error('❌ Database seeding failed:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

seedDatabase()
