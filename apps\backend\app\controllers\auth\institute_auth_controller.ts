import type { HttpContext } from '@adonisjs/core/http'
import User from '#models/user'
import Institute from '#models/institute'
import { instituteRegisterValidator, instituteLoginValidator, refreshTokenValidator } from '#validators/auth_validator'
import InstituteAuthService from '#services/auth/institute_auth_service'

export default class InstituteAuthController {
  private authService = new InstituteAuthService()

  /**
   * @swagger
   * /api/v1/auth/institute/register:
   *   post:
   *     summary: Register a new institute
   *     description: Register a new educational institute with admin user
   *     tags: [Institute Authentication]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - instituteName
   *               - firstName
   *               - lastName
   *               - email
   *               - password
   *               - confirmPassword
   *             properties:
   *               instituteName:
   *                 type: string
   *                 minLength: 2
   *                 maxLength: 255
   *                 example: Harvard University
   *               firstName:
   *                 type: string
   *                 minLength: 2
   *                 maxLength: 50
   *                 example: John
   *               lastName:
   *                 type: string
   *                 minLength: 2
   *                 maxLength: 50
   *                 example: Doe
   *               email:
   *                 type: string
   *                 format: email
   *                 example: <EMAIL>
   *               password:
   *                 type: string
   *                 minLength: 8
   *                 example: password123
   *               confirmPassword:
   *                 type: string
   *                 example: password123
   *     responses:
   *       201:
   *         description: Institute registered successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         user:
   *                           $ref: '#/components/schemas/User'
   *                         institute:
   *                           $ref: '#/components/schemas/Institute'
   *                         tokens:
   *                           $ref: '#/components/schemas/AuthTokens'
   *       400:
   *         description: Validation error or email already exists
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationError'
   *       500:
   *         description: Internal server error
   */
  /**
   * Handle institute registration
   */
  async register({ request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(instituteRegisterValidator)

      // Check if email already exists
      const existingUser = await User.findBy('email', payload.email)
      if (existingUser) {
        return response.status(400).json({
          success: false,
          message: 'Email already exists',
          errors: {
            email: ['This email is already registered']
          }
        })
      }

      // Check if institute email already exists
      const existingInstitute = await Institute.findBy('email', payload.email)
      if (existingInstitute) {
        return response.status(400).json({
          success: false,
          message: 'Institute with this email already exists',
          errors: {
            email: ['An institute with this email already exists']
          }
        })
      }

      // Generate slug from institute name
      const slug = payload.instituteName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-')

      // Create institute
      const institute = await Institute.create({
        name: payload.instituteName,
        slug: slug,
        email: payload.email,
        status: 'pending_approval',
        type: 'college',
        affiliation: 'private',
        subscription_plan: 'free',
        is_verified: false
      })

      // Create institute admin user
      const user = await User.create({
        institute_id: institute.id,
        first_name: payload.firstName,
        last_name: payload.lastName,
        email: payload.email,
        password: payload.password,
        user_type: 'institute_admin',
        status: 'pending_approval',
        is_verified: false
      })

      // Generate tokens
      const tokens = await this.authService.generateTokens(user)

      return response.status(201).json({
        success: true,
        message: 'Institute registered successfully. Please wait for approval.',
        data: {
          user: {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            user_type: user.user_type,
            status: user.status,
            is_verified: user.is_verified
          },
          institute: {
            id: institute.id,
            name: institute.name,
            email: institute.email,
            status: institute.status,
            is_verified: institute.is_verified
          },
          tokens
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      })
    }
  }

  /**
   * @swagger
   * /api/v1/auth/institute/login:
   *   post:
   *     summary: Institute admin/staff login
   *     description: Authenticate institute admin or staff member
   *     tags: [Institute Authentication]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - email
   *               - password
   *             properties:
   *               email:
   *                 type: string
   *                 format: email
   *                 example: <EMAIL>
   *               password:
   *                 type: string
   *                 minLength: 6
   *                 example: password123
   *               remember:
   *                 type: boolean
   *                 example: false
   *     responses:
   *       200:
   *         description: Login successful
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         user:
   *                           $ref: '#/components/schemas/User'
   *                         institute:
   *                           $ref: '#/components/schemas/Institute'
   *                         tokens:
   *                           $ref: '#/components/schemas/AuthTokens'
   *                         permissions:
   *                           type: array
   *                           items:
   *                             type: string
   *       401:
   *         description: Invalid credentials
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationError'
   *       403:
   *         description: Account or institute not active
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationError'
   *       500:
   *         description: Internal server error
   */
  /**
   * Handle institute admin/staff login
   */
  async login({ request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(instituteLoginValidator)

      // Find user by email
      const user = await User.query()
        .where('email', payload.email)
        .whereIn('user_type', ['institute_admin', 'institute_staff'])
        .preload('institute')
        .first()

      if (!user) {
        return response.status(401).json({
          success: false,
          message: 'Invalid credentials',
          errors: {
            email: ['Invalid email or password']
          }
        })
      }

      // Check if institute is active
      if (user.institute && user.institute.status !== 'active') {
        return response.status(403).json({
          success: false,
          message: 'Institute is not active',
          errors: {
            institute: ['Institute has been suspended or deactivated']
          }
        })
      }

      // Verify password
      const isPasswordValid = await user.verifyPassword(payload.password)
      if (!isPasswordValid) {
        return response.status(401).json({
          success: false,
          message: 'Invalid credentials',
          errors: {
            password: ['Invalid email or password']
          }
        })
      }

      // Check if user is active
      if (user.status !== 'active') {
        return response.status(403).json({
          success: false,
          message: 'Account is not active',
          errors: {
            status: ['Your account has been suspended or deactivated']
          }
        })
      }

      // Load user relationships
      await user.load('branch')

      // Generate tokens
      const tokens = await this.authService.generateTokens(user)

      // Update last login
      await this.authService.updateLastLogin(user, request.ip())

      return response.status(200).json({
        success: true,
        message: 'Login successful',
        data: {
          user: user.serialize(),
          institute: user.institute?.serialize(),
          tokens,
          permissions: await this.authService.getUserPermissions(user)
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      })
    }
  }

  /**
   * @swagger
   * /api/v1/auth/institute/me:
   *   get:
   *     summary: Get current user info
   *     description: Get authenticated institute user information
   *     tags: [Institute Authentication]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: User information retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         user:
   *                           $ref: '#/components/schemas/User'
   *                         institute:
   *                           $ref: '#/components/schemas/Institute'
   *                         permissions:
   *                           type: array
   *                           items:
   *                             type: string
   *       401:
   *         description: Unauthorized - Invalid or missing token
   *       500:
   *         description: Internal server error
   */
  /**
   * Get current user info
   */
  async me({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      await user.load('institute')
      await user.load('branch')

      return response.status(200).json({
        success: true,
        data: {
          user: user.serialize(),
          institute: user.institute?.serialize(),
          permissions: await this.authService.getUserPermissions(user)
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      })
    }
  }

  /**
   * Get dashboard data
   */
  async dashboard({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      const dashboardData = await this.authService.getDashboardData(user)

      return response.status(200).json({
        success: true,
        data: dashboardData
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      })
    }
  }

  /**
   * Refresh access token
   */
  async refresh({ request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(refreshTokenValidator)
      const tokens = await this.authService.refreshTokens(payload.refreshToken)

      return response.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: { tokens }
      })
    } catch (error) {
      return response.status(401).json({
        success: false,
        message: 'Invalid refresh token',
        error: error.message
      })
    }
  }

  /**
   * Logout user
   */
  async logout({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      await this.authService.logout(user)

      return response.status(200).json({
        success: true,
        message: 'Logged out successfully'
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      })
    }
  }
}
