{"version": 3, "file": "003_create_users_table.js", "sourceRoot": "", "sources": ["../../../database/migrations/003_create_users_table.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAA;AAEnD,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IAC3B,SAAS,GAAG,OAAO,CAAA;IAE7B,KAAK,CAAC,EAAE;QACN,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAChD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAA;YAGhC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAA;YAC9C,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YACzC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAA;YAG3C,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAA;YAC7C,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAA;YAC5C,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YACpC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGtC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;gBACjB,aAAa;gBACb,WAAW;gBACX,iBAAiB;gBACjB,iBAAiB;gBACjB,SAAS;aACV,CAAC,CAAC,WAAW,EAAE,CAAA;YAChB,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YAGxF,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAA;YACnD,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;YAEzF,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAA;YAChD,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;YAGpF,KAAK,CAAC,SAAS,CAAC,mBAAmB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAChE,KAAK,CAAC,SAAS,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC5D,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC5C,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACpD,KAAK,CAAC,MAAM,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGjD,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGpC,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YACpE,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YACpE,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGzD,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAA;YACrB,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;YACvB,KAAK,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,CAAC,CAAA;YAC7B,KAAK,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;YAC1B,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAA;YAC3B,KAAK,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC,CAAA;QAC9C,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACvC,CAAC;CACF"}