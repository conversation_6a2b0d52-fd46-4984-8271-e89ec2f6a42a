"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/store/slices/authSlice.ts":
/*!***************************************!*\
  !*** ./src/store/slices/authSlice.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCredentials: function() { return /* binding */ clearCredentials; },\n/* harmony export */   clearError: function() { return /* binding */ clearError; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   initializeAuth: function() { return /* binding */ initializeAuth; },\n/* harmony export */   loginInstitute: function() { return /* binding */ loginInstitute; },\n/* harmony export */   logoutUser: function() { return /* binding */ logoutUser; },\n/* harmony export */   registerInstitute: function() { return /* binding */ registerInstitute; },\n/* harmony export */   setCredentials: function() { return /* binding */ setCredentials; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(app-pages-browser)/../../node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/authService */ \"(app-pages-browser)/./src/services/authService.ts\");\n\n\nconst initialState = {\n    user: null,\n    institute: null,\n    permissions: [],\n    isAuthenticated: false,\n    isLoading: false,\n    error: null,\n    tokens: {\n        access_token: null,\n        refresh_token: null\n    }\n};\n// Async thunks\nconst registerInstitute = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createAsyncThunk)(\"auth/register\", async (data, param)=>{\n    let { rejectWithValue } = param;\n    try {\n        const response = await _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].register(data);\n        // Store tokens\n        _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setTokens(response.data.tokens);\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        return rejectWithValue(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Registration failed\");\n    }\n});\nconst loginInstitute = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createAsyncThunk)(\"auth/login\", async (data, param)=>{\n    let { rejectWithValue } = param;\n    try {\n        const response = await _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].login(data);\n        // Store tokens\n        _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setTokens(response.data.tokens);\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        return rejectWithValue(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Login failed\");\n    }\n});\nconst getCurrentUser = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createAsyncThunk)(\"auth/getCurrentUser\", async (_, param)=>{\n    let { rejectWithValue } = param;\n    try {\n        const response = await _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getCurrentUser();\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        return rejectWithValue(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to get user info\");\n    }\n});\nconst logoutUser = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createAsyncThunk)(\"auth/logout\", async (_, param)=>{\n    let { rejectWithValue } = param;\n    try {\n        const response = await _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].logout();\n        // Clear tokens from storage\n        _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearTokens();\n        return response;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        // Even if API call fails, clear tokens locally\n        _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearTokens();\n        return rejectWithValue(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Logout failed\");\n    }\n});\nconst authSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.createSlice)({\n    name: \"auth\",\n    initialState,\n    reducers: {\n        clearError: (state)=>{\n            state.error = null;\n        },\n        setCredentials: (state, action)=>{\n            state.user = action.payload.user;\n            state.institute = action.payload.institute;\n            state.permissions = action.payload.permissions;\n            state.isAuthenticated = true;\n        },\n        clearCredentials: (state)=>{\n            state.user = null;\n            state.institute = null;\n            state.permissions = [];\n            state.isAuthenticated = false;\n            state.tokens.access_token = null;\n            state.tokens.refresh_token = null;\n        },\n        initializeAuth: (state)=>{\n            const accessToken = _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAccessToken();\n            const refreshToken = _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRefreshToken();\n            if (accessToken && refreshToken) {\n                state.tokens.access_token = accessToken;\n                state.tokens.refresh_token = refreshToken;\n                state.isAuthenticated = true;\n            }\n        }\n    },\n    extraReducers: (builder)=>{\n        builder// Register\n        .addCase(registerInstitute.pending, (state)=>{\n            state.isLoading = true;\n            state.error = null;\n        }).addCase(registerInstitute.fulfilled, (state, action)=>{\n            state.isLoading = false;\n            state.user = action.payload.user;\n            state.institute = action.payload.institute;\n            state.permissions = action.payload.permissions || [];\n            state.isAuthenticated = true;\n            state.tokens.access_token = action.payload.tokens.accessToken;\n            state.tokens.refresh_token = action.payload.tokens.refreshToken || null;\n        }).addCase(registerInstitute.rejected, (state, action)=>{\n            state.isLoading = false;\n            state.error = action.payload;\n        })// Login\n        .addCase(loginInstitute.pending, (state)=>{\n            state.isLoading = true;\n            state.error = null;\n        }).addCase(loginInstitute.fulfilled, (state, action)=>{\n            state.isLoading = false;\n            state.user = action.payload.user;\n            state.institute = action.payload.institute;\n            state.permissions = action.payload.permissions || [];\n            state.isAuthenticated = true;\n            state.tokens.access_token = action.payload.tokens.access_token;\n            state.tokens.refresh_token = action.payload.tokens.refresh_token;\n        }).addCase(loginInstitute.rejected, (state, action)=>{\n            state.isLoading = false;\n            state.error = action.payload;\n        })// Get current user\n        .addCase(getCurrentUser.pending, (state)=>{\n            state.isLoading = true;\n        }).addCase(getCurrentUser.fulfilled, (state, action)=>{\n            state.isLoading = false;\n            state.user = action.payload.user;\n            state.institute = action.payload.institute;\n            state.permissions = action.payload.permissions;\n            state.isAuthenticated = true;\n        }).addCase(getCurrentUser.rejected, (state, action)=>{\n            state.isLoading = false;\n            state.error = action.payload;\n            state.isAuthenticated = false;\n        })// Logout\n        .addCase(logoutUser.fulfilled, (state)=>{\n            state.user = null;\n            state.institute = null;\n            state.permissions = [];\n            state.isAuthenticated = false;\n            state.tokens.access_token = null;\n            state.tokens.refresh_token = null;\n            state.isLoading = false;\n            state.error = null;\n        });\n    }\n});\nconst { clearError, setCredentials, clearCredentials, initializeAuth } = authSlice.actions;\n/* harmony default export */ __webpack_exports__[\"default\"] = (authSlice.reducer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/slices/authSlice.ts\n"));

/***/ })

});