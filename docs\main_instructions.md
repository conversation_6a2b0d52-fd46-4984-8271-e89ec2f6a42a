Fix the user session tracking issue: When a user logs in successfully, the system is not storing session data in the user_sessions table in the database. 

Please implement the following:
1. Update the login endpoint to capture and store session information including:
   - Device information (device type, browser, operating system)
   - Network information (IP address, location if available)
   - Session details (login timestamp, token information)
   - Security flags (is_mobile, is_trusted_device, etc.)

2. Ensure the session data is properly inserted into the user_sessions table during the login process

3. Test that session records are being created when users log in through the institute admin login flow

The user_sessions table already exists with 35 columns for comprehensive session tracking, but currently no data is being inserted during login.

