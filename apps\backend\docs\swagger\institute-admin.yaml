paths:
  # Institute Admin Authentication
  /auth/institute/register:
    post:
      tags:
        - Institute Admin Authentication
      summary: Institute Registration
      description: Register a new institute with admin user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - instituteName
                - firstName
                - lastName
                - email
                - password
                - confirmPassword
              properties:
                instituteName:
                  type: string
                  example: "Demo University"
                firstName:
                  type: string
                  example: "John"
                lastName:
                  type: string
                  example: "Doe"
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  minLength: 8
                  example: "admin123"
                confirmPassword:
                  type: string
                  format: password
                  example: "admin123"
      responses:
        201:
          description: Institute registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Institute registered successfully. Please wait for approval."
                  data:
                    type: object
                    properties:
                      institute:
                        $ref: '#/components/schemas/Institute'
                      user:
                        $ref: '#/components/schemas/User'
        400:
          description: Institute already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        422:
          $ref: '#/components/responses/ValidationError'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/institute/login:
    post:
      tags:
        - Institute Admin Authentication
      summary: Institute Admin Login
      description: Authenticate institute admin or staff users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  example: "admin123"
      responses:
        200:
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Login successful"
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      institute:
                        $ref: '#/components/schemas/Institute'
                      tokens:
                        $ref: '#/components/schemas/AuthTokens'
                      permissions:
                        type: array
                        items:
                          type: string
                        example: ["students.manage", "courses.create", "analytics.view"]
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          description: Institute not active
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        422:
          $ref: '#/components/responses/ValidationError'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/institute/refresh:
    post:
      tags:
        - Institute Admin Authentication
      summary: Refresh Access Token
      description: Refresh the access token using refresh token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refresh_token
              properties:
                refresh_token:
                  type: string
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      responses:
        200:
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Token refreshed successfully"
                  data:
                    type: object
                    properties:
                      tokens:
                        $ref: '#/components/schemas/AuthTokens'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/institute/me:
    get:
      tags:
        - Institute Admin Authentication
      summary: Get Current User
      description: Get current authenticated institute admin user information
      security:
        - bearerAuth: []
      responses:
        200:
          description: User information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      institute:
                        $ref: '#/components/schemas/Institute'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/institute/dashboard:
    get:
      tags:
        - Institute Admin Authentication
      summary: Get Dashboard Data
      description: Get institute admin dashboard statistics and data
      security:
        - bearerAuth: []
      responses:
        200:
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      stats:
                        type: object
                        properties:
                          totalStudents:
                            type: integer
                            example: 450
                          totalCourses:
                            type: integer
                            example: 25
                          totalInstructors:
                            type: integer
                            example: 15
                          activeEnrollments:
                            type: integer
                            example: 380
                      recentEnrollments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            student:
                              $ref: '#/components/schemas/User'
                            course:
                              type: object
                              properties:
                                id:
                                  type: integer
                                title:
                                  type: string
                            enrolledAt:
                              type: string
                              format: date-time
                      recentCourses:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            title:
                              type: string
                            instructor:
                              $ref: '#/components/schemas/User'
                            studentsCount:
                              type: integer
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/institute/logout:
    post:
      tags:
        - Institute Admin Authentication
      summary: Logout
      description: Logout and invalidate current session
      security:
        - bearerAuth: []
      responses:
        200:
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Logout successful"
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  # Student Management
  /institute-admin/students:
    get:
      tags:
        - Institute Admin - Student Management
      summary: List Students
      description: Get paginated list of students in the institute
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, suspended]
        - name: search
          in: query
          schema:
            type: string
      responses:
        200:
          description: Students retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      students:
                        type: array
                        items:
                          $ref: '#/components/schemas/User'
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        500:
          $ref: '#/components/responses/InternalServerError'

  # Course Management
  /institute-admin/courses:
    get:
      tags:
        - Institute Admin - Course Management
      summary: List Courses
      description: Get paginated list of courses in the institute
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, draft]
        - name: instructor_id
          in: query
          schema:
            type: integer
      responses:
        200:
          description: Courses retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      courses:
                        type: array
                        items:
                          $ref: '#/components/schemas/Course'
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        500:
          $ref: '#/components/responses/InternalServerError'

  # Analytics
  /institute-admin/analytics/overview:
    get:
      tags:
        - Institute Admin - Analytics
      summary: Get Analytics Overview
      description: Get comprehensive analytics overview for institute
      security:
        - bearerAuth: []
      responses:
        200:
          description: Analytics overview retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      totalStudents:
                        type: integer
                        example: 450
                      totalCourses:
                        type: integer
                        example: 25
                      totalInstructors:
                        type: integer
                        example: 15
                      activeEnrollments:
                        type: integer
                        example: 380
                      completionRate:
                        type: number
                        example: 78.5
                      averageGrade:
                        type: number
                        example: 85.2
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        500:
          $ref: '#/components/responses/InternalServerError'