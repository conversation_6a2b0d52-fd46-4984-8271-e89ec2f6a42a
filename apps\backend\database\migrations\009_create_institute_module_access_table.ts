import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'institute_module_access'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('institute_id').unsigned().notNullable()
      table.integer('module_id').unsigned().notNullable()
      table.boolean('is_active').defaultTo(true)
      
      // Timestamps
      table.timestamp('created_at', { useTz: true }).defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).defaultTo(this.now())
      
      // Unique constraint to prevent duplicate institute-module pairs
      table.unique(['institute_id', 'module_id'])
      
      // Foreign keys
      table.foreign('institute_id').references('id').inTable('institutes').onDelete('CASCADE')
      table.foreign('module_id').references('id').inTable('modules').onDelete('CASCADE')
      
      // Indexes
      table.index(['institute_id'])
      table.index(['module_id'])
      table.index(['is_active'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}