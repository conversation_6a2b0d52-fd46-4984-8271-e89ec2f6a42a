import 'reflect-metadata';
import { Ignitor } from '@adonisjs/core';
const APP_ROOT = new URL('../', import.meta.url);
const IMPORTER = (filePath) => {
    if (filePath.startsWith('./') || filePath.startsWith('../')) {
        return import(new URL(filePath, APP_ROOT).href);
    }
    return import(filePath);
};
try {
    const ignitor = new Ignitor(APP_ROOT, { importer: IMPORTER });
    await ignitor.httpServer().start();
}
catch (error) {
    process.exitCode = 1;
    console.error(error);
}
//# sourceMappingURL=server.js.map