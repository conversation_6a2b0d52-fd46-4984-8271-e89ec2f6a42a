import { DateTime } from 'luxon';
import jwt from 'jsonwebtoken';
import env from '#start/env';
import User from '#models/user';
import Institute from '#models/institute';
export default class SuperAdminAuthService {
    async generateTokens(user) {
        const payload = {
            userId: user.id,
            email: user.email,
            role: user.role,
            instituteId: user.instituteId,
            type: 'access'
        };
        const accessToken = jwt.sign(payload, env.get('JWT_SECRET'), {
            expiresIn: env.get('JWT_EXPIRES_IN', '24h'),
            issuer: 'lms-platform',
            audience: 'lms-users'
        });
        const refreshPayload = {
            userId: user.id,
            type: 'refresh'
        };
        const refreshToken = jwt.sign(refreshPayload, env.get('JWT_SECRET'), {
            expiresIn: env.get('JWT_REFRESH_EXPIRES_IN', '7d'),
            issuer: 'lms-platform',
            audience: 'lms-users'
        });
        return {
            accessToken,
            refreshToken,
            tokenType: 'Bearer',
            expiresIn: env.get('JWT_EXPIRES_IN', '24h')
        };
    }
    async refreshTokens(refreshToken) {
        try {
            const decoded = jwt.verify(refreshToken, env.get('JWT_SECRET'));
            if (decoded.type !== 'refresh') {
                throw new Error('Invalid token type');
            }
            const user = await User.find(decoded.userId);
            if (!user || !['super_admin', 'lms_admin'].includes(user.role)) {
                throw new Error('User not found or invalid role');
            }
            return this.generateTokens(user);
        }
        catch (error) {
            throw new Error('Invalid refresh token');
        }
    }
    async updateLastLogin(user, ipAddress) {
        user.lastLoginAt = DateTime.now();
        user.lastLoginIp = ipAddress;
        await user.save();
    }
    async getUserPermissions(user) {
        const basePermissions = ['profile.read', 'profile.update'];
        if (user.role === 'super_admin') {
            return [
                ...basePermissions,
                'institutes.create',
                'institutes.read',
                'institutes.update',
                'institutes.delete',
                'users.create',
                'users.read',
                'users.update',
                'users.delete',
                'system.settings',
                'system.analytics',
                'system.logs',
                'billing.read',
                'billing.update'
            ];
        }
        if (user.role === 'lms_admin') {
            return [
                ...basePermissions,
                'institutes.read',
                'institutes.update',
                'users.read',
                'users.update',
                'system.analytics',
                'billing.read'
            ];
        }
        return basePermissions;
    }
    async getDashboardData(user) {
        const [totalInstitutes, activeInstitutes, totalUsers, activeUsers, recentInstitutes, systemStats] = await Promise.all([
            Institute.query().count('* as total'),
            Institute.query().where('status', 'active').count('* as total'),
            User.query().count('* as total'),
            User.query().where('status', 'active').count('* as total'),
            Institute.query()
                .orderBy('created_at', 'desc')
                .limit(5)
                .select('id', 'name', 'code', 'status', 'subscription_plan', 'created_at'),
            this.getSystemStats()
        ]);
        return {
            stats: {
                totalInstitutes: totalInstitutes[0].$extras.total,
                activeInstitutes: activeInstitutes[0].$extras.total,
                totalUsers: totalUsers[0].$extras.total,
                activeUsers: activeUsers[0].$extras.total
            },
            recentInstitutes: recentInstitutes.map(institute => institute.serialize()),
            systemStats
        };
    }
    async getSystemStats() {
        return {
            cpuUsage: Math.floor(Math.random() * 100),
            memoryUsage: Math.floor(Math.random() * 100),
            diskUsage: Math.floor(Math.random() * 100),
            uptime: '99.9%',
            activeConnections: Math.floor(Math.random() * 1000) + 500
        };
    }
    async logout(user) {
        user.updatedAt = DateTime.now();
        await user.save();
    }
}
//# sourceMappingURL=super_admin_auth_service.js.map