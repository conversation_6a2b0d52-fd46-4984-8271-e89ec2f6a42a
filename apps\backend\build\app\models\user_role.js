var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm';
import User from './user.js';
import Role from './role.js';
import Institute from './institute.js';
export default class UserRole extends BaseModel {
    get roleKey() {
        return `${this.role?.name || 'unknown'}`;
    }
    get userFullName() {
        return this.user?.fullName || 'Unknown User';
    }
    get instituteName() {
        return this.institute?.name || 'Unknown Institute';
    }
    serialize() {
        return {
            id: this.id,
            userId: this.userId,
            roleId: this.roleId,
            instituteId: this.instituteId,
            roleKey: this.roleKey,
            userFullName: this.userFullName,
            instituteName: this.instituteName,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], UserRole.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], UserRole.prototype, "userId", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], UserRole.prototype, "roleId", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], UserRole.prototype, "instituteId", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], UserRole.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], UserRole.prototype, "updatedAt", void 0);
__decorate([
    belongsTo(() => User),
    __metadata("design:type", Object)
], UserRole.prototype, "user", void 0);
__decorate([
    belongsTo(() => Role),
    __metadata("design:type", Object)
], UserRole.prototype, "role", void 0);
__decorate([
    belongsTo(() => Institute),
    __metadata("design:type", Object)
], UserRole.prototype, "institute", void 0);
//# sourceMappingURL=user_role.js.map