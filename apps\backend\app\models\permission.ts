import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import Module from './module.js'
import RoleModulePermission from './role_module_permission.js'

export default class Permission extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare displayName: string

  @column()
  declare description: string | null

  @column()
  declare moduleId: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationships
   */
  @belongsTo(() => Module)
  declare module: BelongsTo<typeof Module>

  @hasMany(() => RoleModulePermission)
  declare rolePermissions: HasMany<typeof RoleModulePermission>

  /**
   * Computed properties
   */
  get fullName() {
    return `${this.module?.name || 'unknown'}.${this.name}`
  }

  /**
   * Serialize permission data for API responses
   */
  serialize() {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      description: this.description,
      moduleId: this.moduleId,
      fullName: this.fullName,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
