import { Env } from '@adonisjs/core/env';
export default await Env.create(new URL('../', import.meta.url), {
    NODE_ENV: Env.schema.enum(['development', 'production', 'test']),
    PORT: Env.schema.number(),
    APP_KEY: Env.schema.string(),
    HOST: Env.schema.string({ format: 'host' }),
    LOG_LEVEL: Env.schema.string(),
    DB_CONNECTION: Env.schema.string(),
    DB_HOST: Env.schema.string({ format: 'host' }),
    DB_PORT: Env.schema.number(),
    DB_USER: Env.schema.string(),
    DB_PASSWORD: Env.schema.string.optional(),
    DB_DATABASE: Env.schema.string(),
    SESSION_DRIVER: Env.schema.enum(['cookie', 'memory']),
    HASH_DRIVER: Env.schema.enum(['scrypt', 'bcrypt', 'argon']),
    JWT_SECRET: Env.schema.string(),
    JWT_EXPIRES_IN: Env.schema.string.optional(),
    JWT_REFRESH_EXPIRES_IN: Env.schema.string.optional(),
    CORS_ENABLED: Env.schema.boolean.optional(),
    CORS_ORIGIN: Env.schema.string.optional(),
    CORS_METHODS: Env.schema.string.optional(),
    CORS_HEADERS: Env.schema.string.optional(),
    SMTP_HOST: Env.schema.string.optional(),
    SMTP_PORT: Env.schema.number.optional(),
    SMTP_USERNAME: Env.schema.string.optional(),
    SMTP_PASSWORD: Env.schema.string.optional(),
    MAIL_FROM_ADDRESS: Env.schema.string.optional(),
    MAIL_FROM_NAME: Env.schema.string.optional(),
    REDIS_CONNECTION: Env.schema.string.optional(),
    REDIS_HOST: Env.schema.string.optional(),
    REDIS_PORT: Env.schema.number.optional(),
    REDIS_PASSWORD: Env.schema.string.optional(),
    DRIVE_DISK: Env.schema.string.optional(),
    RATE_LIMIT_ENABLED: Env.schema.boolean.optional(),
    RATE_LIMIT_MAX_REQUESTS: Env.schema.number.optional(),
    RATE_LIMIT_WINDOW_MS: Env.schema.number.optional(),
    STRIPE_PUBLIC_KEY: Env.schema.string.optional(),
    STRIPE_SECRET_KEY: Env.schema.string.optional(),
    STRIPE_WEBHOOK_SECRET: Env.schema.string.optional(),
    S3_KEY: Env.schema.string.optional(),
    S3_SECRET: Env.schema.string.optional(),
    S3_BUCKET: Env.schema.string.optional(),
    S3_REGION: Env.schema.string.optional(),
    FRONTEND_URL: Env.schema.string.optional(),
    BACKEND_URL: Env.schema.string.optional(),
    ENABLE_SUBDOMAINS: Env.schema.boolean.optional(),
    DEFAULT_DOMAIN: Env.schema.string.optional(),
});
//# sourceMappingURL=env.js.map