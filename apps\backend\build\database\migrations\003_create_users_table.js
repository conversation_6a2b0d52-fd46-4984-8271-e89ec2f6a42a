import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'users';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.string('email', 255).nullable().unique();
            table.string('student_id', 50).nullable();
            table.string('password', 255).notNullable();
            table.string('first_name', 100).notNullable();
            table.string('last_name', 100).notNullable();
            table.string('phone', 20).nullable();
            table.string('avatar', 500).nullable();
            table.enum('role', [
                'super_admin',
                'lms_admin',
                'institute_admin',
                'institute_staff',
                'student'
            ]).notNullable();
            table.enum('status', ['active', 'inactive', 'suspended', 'pending']).defaultTo('active');
            table.integer('institute_id').unsigned().nullable();
            table.foreign('institute_id').references('id').inTable('institutes').onDelete('SET NULL');
            table.integer('branch_id').unsigned().nullable();
            table.foreign('branch_id').references('id').inTable('branches').onDelete('SET NULL');
            table.timestamp('email_verified_at', { useTz: true }).nullable();
            table.timestamp('last_login_at', { useTz: true }).nullable();
            table.string('last_login_ip', 45).nullable();
            table.boolean('two_factor_enabled').defaultTo(false);
            table.string('two_factor_secret', 255).nullable();
            table.json('preferences').nullable();
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('deleted_at', { useTz: true }).nullable();
            table.index(['role']);
            table.index(['status']);
            table.index(['institute_id']);
            table.index(['branch_id']);
            table.index(['created_at']);
            table.unique(['institute_id', 'student_id']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=003_create_users_table.js.map