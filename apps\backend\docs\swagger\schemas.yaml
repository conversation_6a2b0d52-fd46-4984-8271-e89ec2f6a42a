components:
  schemas:
    Course:
      type: object
      properties:
        id:
          type: integer
          example: 1
        title:
          type: string
          example: "Introduction to Computer Science"
        slug:
          type: string
          example: "intro-computer-science"
        description:
          type: string
          example: "A comprehensive introduction to computer science fundamentals"
        shortDescription:
          type: string
          example: "Learn CS fundamentals"
        thumbnail:
          type: string
          example: "https://example.com/course-thumbnail.jpg"
        price:
          type: number
          example: 299.99
        discountPrice:
          type: number
          example: 199.99
        currency:
          type: string
          example: "USD"
        duration:
          type: integer
          example: 40
        level:
          type: string
          enum: [beginner, intermediate, advanced]
          example: "beginner"
        language:
          type: string
          example: "English"
        status:
          type: string
          enum: [draft, published, archived]
          example: "published"
        maxStudents:
          type: integer
          example: 100
        enrolledStudents:
          type: integer
          example: 45
        rating:
          type: number
          example: 4.5
        totalRatings:
          type: integer
          example: 120
        instituteId:
          type: integer
          example: 1
        instructorId:
          type: integer
          example: 5
        instructor:
          $ref: '#/components/schemas/User'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Enrollment:
      type: object
      properties:
        id:
          type: integer
          example: 1
        userId:
          type: integer
          example: 10
        courseId:
          type: integer
          example: 1
        status:
          type: string
          enum: [enrolled, completed, dropped, suspended]
          example: "enrolled"
        progress:
          type: number
          example: 75.5
        grade:
          type: string
          example: "A"
        enrolledAt:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time
        droppedAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Branch:
      type: object
      properties:
        id:
          type: integer
          example: 1
        instituteId:
          type: integer
          example: 1
        name:
          type: string
          example: "Main Campus"
        code:
          type: string
          example: "MAIN"
        description:
          type: string
          example: "Main campus branch"
        address:
          type: string
          example: "123 University Ave"
        city:
          type: string
          example: "New York"
        state:
          type: string
          example: "NY"
        country:
          type: string
          example: "USA"
        zipCode:
          type: string
          example: "10001"
        phone:
          type: string
          example: "+1234567890"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        status:
          type: string
          enum: [active, inactive]
          example: "active"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Module:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "students"
        displayName:
          type: string
          example: "Student Management"
        description:
          type: string
          example: "Manage student accounts and enrollments"
        isActive:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Role:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "institute_admin"
        displayName:
          type: string
          example: "Institute Administrator"
        description:
          type: string
          example: "Full access to institute management"
        isSystemRole:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Permission:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "create"
        displayName:
          type: string
          example: "Create"
        description:
          type: string
          example: "Permission to create new records"
        moduleId:
          type: integer
          example: 1
        module:
          $ref: '#/components/schemas/Module'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Pagination:
      type: object
      properties:
        currentPage:
          type: integer
          example: 1
        perPage:
          type: integer
          example: 20
        total:
          type: integer
          example: 150
        lastPage:
          type: integer
          example: 8
        hasNextPage:
          type: boolean
          example: true
        hasPrevPage:
          type: boolean
          example: false

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "Authentication required"
              error:
                type: string
                example: "Token not provided or invalid"

    Forbidden:
      description: Access denied
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "Access denied"
              error:
                type: string
                example: "Insufficient permissions"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "Resource not found"
              error:
                type: string
                example: "The requested resource does not exist"

    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ValidationError'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "Internal server error"
              error:
                type: string
                example: "An unexpected error occurred"
