"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"f4cfe05767ad\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGIyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY0Y2ZlMDU3NjdhZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/authService.ts":
/*!*************************************!*\
  !*** ./src/services/authService.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: function() { return /* binding */ authService; }\n/* harmony export */ });\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\nclass AuthService {\n    async register(data) {\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/institute/register\", data);\n        return response.data;\n    }\n    async login(data) {\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/institute/login\", data);\n        return response.data;\n    }\n    async getCurrentUser() {\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/auth/institute/me\");\n        return response.data;\n    }\n    async logout() {\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/logout\");\n        return response.data;\n    }\n    async refreshToken(refreshToken) {\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/institute/refresh\", {\n            refreshToken\n        });\n        return response.data;\n    }\n    // Token management\n    setTokens(tokens) {\n        localStorage.setItem(\"access_token\", tokens.access_token);\n        localStorage.setItem(\"refresh_token\", tokens.refresh_token);\n    }\n    getAccessToken() {\n        return localStorage.getItem(\"access_token\");\n    }\n    getRefreshToken() {\n        return localStorage.getItem(\"refresh_token\");\n    }\n    clearTokens() {\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"refresh_token\");\n        localStorage.removeItem(\"user\");\n    }\n    isAuthenticated() {\n        return !!this.getAccessToken();\n    }\n}\nconst authService = new AuthService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (authService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/authService.ts\n"));

/***/ })

});