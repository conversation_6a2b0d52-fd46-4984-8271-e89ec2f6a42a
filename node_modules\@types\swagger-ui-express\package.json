{"name": "@types/swagger-ui-express", "version": "4.1.8", "description": "TypeScript definitions for swagger-ui-express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/swagger-ui-express", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/dmit<PERSON><PERSON>ozhny"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian", "url": "https://github.com/ffflorian"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/swagger-ui-express"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/serve-static": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "da615a60853b98ad26eb83ad15d5fe4465fb5c186f522e504476963bbf3242ec", "typeScriptVersion": "5.0"}