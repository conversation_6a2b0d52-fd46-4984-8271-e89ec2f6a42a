import type { Metadata } from 'next'
import { <PERSON>, <PERSON><PERSON>, Merriweather } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { Providers } from '@/store/providers'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })
const lato = Lato({
  subsets: ['latin'],
  weight: ['300', '400', '700', '900'],
  variable: '--font-lato'
})
const merriweather = Merriweather({
  subsets: ['latin'],
  weight: ['300', '400', '700', '900'],
  variable: '--font-merriweather'
})

export const metadata: Metadata = {
  title: 'LMS Platform - Multi-Tenant Learning Management System',
  description: 'A comprehensive multi-tenant LMS SAAS platform for educational institutions',
  keywords: ['LMS', 'Learning Management System', 'Education', 'SAAS', 'Multi-tenant'],
  authors: [{ name: 'LMS Platform Team' }],
  creator: 'LMS Platform',
  publisher: 'LMS Platform',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://lms-platform.com'),
  openGraph: {
    title: 'LMS Platform - Multi-Tenant Learning Management System',
    description: 'A comprehensive multi-tenant LMS SAAS platform for educational institutions',
    url: 'https://lms-platform.com',
    siteName: 'LMS Platform',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'LMS Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'LMS Platform - Multi-Tenant Learning Management System',
    description: 'A comprehensive multi-tenant LMS SAAS platform for educational institutions',
    images: ['/twitter-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} ${lato.variable} ${merriweather.variable} antialiased`}>
        <ThemeProvider
          defaultTheme="system"
          storageKey="lms-theme"
        >
          <Providers>
            {children}
            <Toaster />
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  )
}
