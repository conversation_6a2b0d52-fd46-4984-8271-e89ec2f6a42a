import UserSession from '#models/user_session';
import SessionService from '#services/session_service';
export default class UserSessionsController {
    sessionService = new SessionService();
    async index({ auth, response }) {
        try {
            const user = auth.user;
            const sessions = await this.sessionService.getUserActiveSessions(user.id);
            return response.status(200).json({
                success: true,
                data: {
                    sessions: sessions.map(session => session.serializeMinimal()),
                    total: sessions.length
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Failed to fetch sessions',
                error: error.message
            });
        }
    }
    async show({ auth, params, response }) {
        try {
            const user = auth.user;
            const session = await UserSession.query()
                .where('id', params.id)
                .where('userId', user.id)
                .first();
            if (!session) {
                return response.status(404).json({
                    success: false,
                    message: 'Session not found'
                });
            }
            return response.status(200).json({
                success: true,
                data: session.serialize()
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Failed to fetch session details',
                error: error.message
            });
        }
    }
    async revoke({ auth, params, response }) {
        try {
            const user = auth.user;
            const session = await UserSession.query()
                .where('id', params.id)
                .where('userId', user.id)
                .where('status', 'active')
                .first();
            if (!session) {
                return response.status(404).json({
                    success: false,
                    message: 'Active session not found'
                });
            }
            await session.markAsRevoked('Manually revoked by user');
            return response.status(200).json({
                success: true,
                message: 'Session revoked successfully'
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Failed to revoke session',
                error: error.message
            });
        }
    }
    async revokeAll({ auth, response }) {
        try {
            const user = auth.user;
            const currentSessionId = this.getCurrentSessionId(auth);
            const revokedCount = await this.sessionService.revokeAllUserSessions(user.id, currentSessionId);
            return response.status(200).json({
                success: true,
                message: `${revokedCount} sessions revoked successfully`,
                data: {
                    revokedCount
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Failed to revoke sessions',
                error: error.message
            });
        }
    }
    async trustDevice({ auth, params, response }) {
        try {
            const user = auth.user;
            const session = await UserSession.query()
                .where('id', params.id)
                .where('userId', user.id)
                .first();
            if (!session) {
                return response.status(404).json({
                    success: false,
                    message: 'Session not found'
                });
            }
            await session.markAsTrusted();
            return response.status(200).json({
                success: true,
                message: 'Device marked as trusted'
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Failed to trust device',
                error: error.message
            });
        }
    }
    async analytics({ auth, response }) {
        try {
            const user = auth.user;
            const sessions = await UserSession.query()
                .where('userId', user.id)
                .where('loginAt', '>', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
                .orderBy('loginAt', 'desc');
            const analytics = {
                totalSessions: sessions.length,
                activeSessions: sessions.filter(s => s.status === 'active').length,
                deviceTypes: this.groupBy(sessions, 'deviceType'),
                browsers: this.groupBy(sessions, 'browserName'),
                operatingSystems: this.groupBy(sessions, 'operatingSystem'),
                countries: this.groupBy(sessions, 'country'),
                suspiciousAttempts: sessions.filter(s => s.isSuspicious).length,
                averageSessionDuration: this.calculateAverageSessionDuration(sessions),
                loginTrends: this.calculateLoginTrends(sessions),
            };
            return response.status(200).json({
                success: true,
                data: analytics
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Failed to fetch session analytics',
                error: error.message
            });
        }
    }
    async securityAlerts({ auth, response }) {
        try {
            const user = auth.user;
            const suspiciousSessions = await UserSession.query()
                .where('userId', user.id)
                .where('isSuspicious', true)
                .where('loginAt', '>', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
                .orderBy('loginAt', 'desc');
            const alerts = suspiciousSessions.map(session => ({
                id: session.id,
                type: 'suspicious_login',
                severity: 'medium',
                message: session.securityNotes || 'Suspicious login detected',
                timestamp: session.loginAt,
                details: {
                    location: `${session.city || 'Unknown'}, ${session.country || 'Unknown'}`,
                    device: `${session.deviceType} - ${session.operatingSystem}`,
                    browser: session.browserName,
                    ipAddress: session.ipAddress,
                }
            }));
            return response.status(200).json({
                success: true,
                data: {
                    alerts,
                    total: alerts.length
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Failed to fetch security alerts',
                error: error.message
            });
        }
    }
    getCurrentSessionId(auth) {
        return auth.user?.currentSessionId;
    }
    groupBy(array, property) {
        return array.reduce((acc, item) => {
            const key = item[property] || 'Unknown';
            acc[key] = (acc[key] || 0) + 1;
            return acc;
        }, {});
    }
    calculateAverageSessionDuration(sessions) {
        const completedSessions = sessions.filter(s => s.logoutAt);
        if (completedSessions.length === 0)
            return 0;
        const totalDuration = completedSessions.reduce((sum, session) => {
            return sum + session.sessionDuration;
        }, 0);
        return Math.round(totalDuration / completedSessions.length);
    }
    calculateLoginTrends(sessions) {
        const trends = {};
        sessions.forEach(session => {
            const date = session.loginAt.toFormat('yyyy-MM-dd');
            trends[date] = (trends[date] || 0) + 1;
        });
        return trends;
    }
}
//# sourceMappingURL=user_sessions_controller.js.map