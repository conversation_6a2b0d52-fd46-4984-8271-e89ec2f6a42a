"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/components/layouts/institute-admin/InstituteAdminHeader.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/layouts/institute-admin/InstituteAdminHeader.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstituteAdminHeader: function() { return /* binding */ InstituteAdminHeader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store */ \"(app-pages-browser)/./src/store/index.ts\");\n/* harmony import */ var _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/slices/authSlice */ \"(app-pages-browser)/./src/store/slices/authSlice.ts\");\n/* harmony import */ var _hooks_use_api_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-api-toast */ \"(app-pages-browser)/./src/hooks/use-api-toast.ts\");\n/* harmony import */ var _shared_headers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/headers */ \"(app-pages-browser)/./src/components/layouts/shared/headers/index.ts\");\n/* __next_internal_client_entry_do_not_use__ InstituteAdminHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction InstituteAdminHeader(param) {\n    let { onMenuToggle } = param;\n    _s();\n    const dispatch = (0,_store__WEBPACK_IMPORTED_MODULE_2__.useAppDispatch)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user: authUser } = (0,_store__WEBPACK_IMPORTED_MODULE_2__.useAppSelector)((state)=>state.auth);\n    const { showLogoutSuccess, handleApiError } = (0,_hooks_use_api_toast__WEBPACK_IMPORTED_MODULE_4__.useApiToast)();\n    // Use real user data from auth state or fallback\n    const user = authUser ? {\n        name: \"\".concat(authUser.first_name || \"\", \" \").concat(authUser.last_name || \"\").trim() || authUser.email,\n        email: authUser.email,\n        role: authUser.user_type === \"institute_admin\" ? \"Institute Admin\" : authUser.user_type === \"institute_staff\" ? \"Institute Staff\" : authUser.user_type || \"User\",\n        avatar: undefined\n    } : {\n        name: \"Institute Admin\",\n        email: \"<EMAIL>\",\n        role: \"Institute Admin\",\n        avatar: undefined\n    };\n    const handleLogout = async ()=>{\n        try {\n            const result = await dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_3__.logoutUser)());\n            // Always show success toast and redirect since tokens are cleared regardless of API response\n            showLogoutSuccess();\n            // Redirect to login page after a short delay\n            setTimeout(()=>{\n                router.push(\"/auth-login/login\");\n            }, 1500);\n        } catch (err) {\n            // Even if there's an error, still redirect since tokens are cleared\n            showLogoutSuccess();\n            setTimeout(()=>{\n                router.push(\"/auth-login/login\");\n            }, 1500);\n        }\n    };\n    const notifications = [\n        {\n            id: \"1\",\n            title: \"New Student Registration\",\n            message: \"5 new students have registered for Computer Science\",\n            time: \"10 minutes ago\",\n            read: false,\n            type: \"info\"\n        },\n        {\n            id: \"2\",\n            title: \"Course Update Required\",\n            message: \"Mathematics 101 needs curriculum review\",\n            time: \"2 hours ago\",\n            read: false,\n            type: \"warning\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_headers__WEBPACK_IMPORTED_MODULE_5__.BaseHeader, {\n        onMenuToggle: onMenuToggle,\n        user: user,\n        notifications: notifications,\n        onLogout: handleLogout,\n        className: \"border-b border-green-200 dark:border-green-800\"\n    }, void 0, false, {\n        fileName: \"C:\\\\wamp64\\\\www\\\\projects\\\\lms_lte\\\\apps\\\\frontend\\\\src\\\\components\\\\layouts\\\\institute-admin\\\\InstituteAdminHeader.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_s(InstituteAdminHeader, \"ik2SbocLvu1zv0f/HYBqgZbPZds=\", false, function() {\n    return [\n        _store__WEBPACK_IMPORTED_MODULE_2__.useAppDispatch,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _store__WEBPACK_IMPORTED_MODULE_2__.useAppSelector,\n        _hooks_use_api_toast__WEBPACK_IMPORTED_MODULE_4__.useApiToast\n    ];\n});\n_c = InstituteAdminHeader;\nvar _c;\n$RefreshReg$(_c, \"InstituteAdminHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layouts/institute-admin/InstituteAdminHeader.tsx\n"));

/***/ })

});