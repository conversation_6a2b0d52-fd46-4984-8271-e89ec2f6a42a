import express from 'express'
import cors from 'cors'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import mysql from 'mysql2/promise'
import swagger<PERSON>SDoc from 'swagger-jsdoc'
import swaggerUi from 'swagger-ui-express'

const app = express()
const PORT = process.env.PORT || 3333

// Database connection
const dbConfig = {
  host: '127.0.0.1',
  port: 3307,
  user: 'root',
  password: '',
  database: 'lms'
}

let db
try {
  db = await mysql.createConnection(dbConfig)
  console.log('✅ Connected to MySQL database')

  // Create tables if they don't exist
  await createTablesIfNotExist()
  console.log('✅ Database tables verified')
} catch (error) {
  console.error('❌ Database connection failed:', error.message)
  console.error('Full error:', error)
  process.exit(1)
}

async function createTablesIfNotExist() {
  try {
    // Create institutes table
    await db.execute(`
      CREATE TABLE IF NOT EXISTS institutes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        code VARCHAR(50) NOT NULL UNIQUE,
        slug VARCHAR(255) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL,
        status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
        subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'basic',
        subscription_status ENUM('trial', 'active', 'expired', 'cancelled') DEFAULT 'trial',
        timezone VARCHAR(50) DEFAULT 'UTC',
        currency VARCHAR(3) DEFAULT 'USD',
        language VARCHAR(5) DEFAULT 'en',
        max_students INT DEFAULT 100,
        max_staff INT DEFAULT 10,
        max_courses INT DEFAULT 50,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    // Create users table
    await db.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        institute_id INT NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('super_admin', 'institute_admin', 'institute_staff', 'instructor', 'student') NOT NULL,
        status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE
      )
    `)

    console.log('📋 Database tables created/verified successfully')
  } catch (error) {
    console.error('❌ Error creating tables:', error.message)
    throw error
  }
}

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    })
  }

  jwt.verify(token, 'your-jwt-secret-key-here', (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Invalid or expired token'
      })
    }
    req.user = user
    next()
  })
}

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'LMS Platform API',
      version: '1.0.0',
      description: 'A comprehensive Learning Management System API',
    },
    servers: [
      {
        url: 'http://localhost:3333',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    }
  },
  apis: ['./server-simple.js']
}

const swaggerSpec = swaggerJSDoc(swaggerOptions)

// Swagger UI
app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec))

// Basic routes
app.get('/', (req, res) => {
  console.log('📍 Root endpoint hit')
  res.json({
    message: 'LMS Backend API is running!',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    documentation: 'http://localhost:3333/docs'
  })
})

app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  })
})

/**
 * @swagger
 * /api/v1/auth/institute/register:
 *   post:
 *     tags:
 *       - Institute Admin Authentication
 *     summary: Institute Registration
 *     description: Register a new institute with admin user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - instituteName
 *               - firstName
 *               - lastName
 *               - email
 *               - password
 *               - confirmPassword
 *             properties:
 *               instituteName:
 *                 type: string
 *                 example: "Demo University"
 *               firstName:
 *                 type: string
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 example: "Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 8
 *                 example: "admin123"
 *               confirmPassword:
 *                 type: string
 *                 format: password
 *                 example: "admin123"
 *     responses:
 *       201:
 *         description: Institute registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Institute registered successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     institute:
 *                       type: object
 *                     user:
 *                       type: object
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
app.post('/api/v1/auth/institute/register', async (req, res) => {
  try {
    const { instituteName, firstName, lastName, email, password, confirmPassword } = req.body

    // Validation
    if (!instituteName || !firstName || !lastName || !email || !password || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required',
        errors: {
          instituteName: !instituteName ? ['Institute name is required'] : undefined,
          firstName: !firstName ? ['First name is required'] : undefined,
          lastName: !lastName ? ['Last name is required'] : undefined,
          email: !email ? ['Email is required'] : undefined,
          password: !password ? ['Password is required'] : undefined,
          confirmPassword: !confirmPassword ? ['Confirm password is required'] : undefined,
        }
      })
    }

    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Passwords do not match',
        errors: {
          confirmPassword: ['Passwords do not match']
        }
      })
    }

    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long',
        errors: {
          password: ['Password must be at least 8 characters long']
        }
      })
    }

    // Check if email already exists
    const [existingUsers] = await db.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    )

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Email already exists',
        errors: {
          email: ['Email already exists']
        }
      })
    }

    // Check if institute name already exists
    const [existingInstitutes] = await db.execute(
      'SELECT id FROM institutes WHERE name = ?',
      [instituteName]
    )

    if (existingInstitutes.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Institute name already exists',
        errors: {
          instituteName: ['Institute name already exists']
        }
      })
    }

    // Create institute
    const slug = instituteName.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-')
    const [instituteResult] = await db.execute(
      `INSERT INTO institutes (name, code, slug, email, status, subscription_plan, subscription_status,
       timezone, currency, language, max_students, max_staff, max_courses, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        instituteName,
        slug.toUpperCase(),
        slug,
        email,
        'pending',
        'basic',
        'trial',
        'UTC',
        'USD',
        'en',
        100,
        10,
        50
      ]
    )

    const instituteId = instituteResult.insertId

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const [userResult] = await db.execute(
      `INSERT INTO users (institute_id, first_name, last_name, email, password, role, status,
       created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [instituteId, firstName, lastName, email, hashedPassword, 'institute_admin', 'pending']
    )

    const userId = userResult.insertId

    // Get created institute and user
    const [institutes] = await db.execute('SELECT * FROM institutes WHERE id = ?', [instituteId])
    const [users] = await db.execute('SELECT id, institute_id, first_name, last_name, email, role, status, created_at FROM users WHERE id = ?', [userId])

    const institute = institutes[0]
    const user = users[0]

    res.status(201).json({
      success: true,
      message: 'Institute registered successfully. Please wait for approval.',
      data: {
        institute: {
          id: institute.id,
          name: institute.name,
          code: institute.code,
          slug: institute.slug,
          email: institute.email,
          status: institute.status,
          subscriptionPlan: institute.subscription_plan,
          subscriptionStatus: institute.subscription_status,
          createdAt: institute.created_at
        },
        user: {
          id: user.id,
          instituteId: user.institute_id,
          firstName: user.first_name,
          lastName: user.last_name,
          email: user.email,
          role: user.role,
          status: user.status,
          createdAt: user.created_at
        }
      }
    })

  } catch (error) {
    console.error('Institute registration error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    })
  }
})

/**
 * @swagger
 * /api/v1/auth/institute/login:
 *   post:
 *     tags:
 *       - Institute Admin Authentication
 *     summary: Institute Admin Login
 *     description: Authenticate institute admin or staff users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 example: "admin123"
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 *       500:
 *         description: Internal server error
 */
app.post('/api/v1/auth/institute/login', async (req, res) => {
  try {
    console.log('🔐 Login attempt received:', req.body.email)
    const { email, password } = req.body

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      })
    }

    // Find user with institute
    const [users] = await db.execute(
      `SELECT u.*, i.name as institute_name, i.status as institute_status
       FROM users u
       JOIN institutes i ON u.institute_id = i.id
       WHERE u.email = ? AND u.role IN ('institute_admin', 'institute_staff')`,
      [email]
    )

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      })
    }

    const user = users[0]

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      })
    }

    // Check if institute is active
    if (user.institute_status !== 'active') {
      return res.status(403).json({
        success: false,
        message: 'Institute is not active. Please contact support.'
      })
    }

    // Generate JWT token
    const tokenId = `token_${user.id}_${Date.now()}`
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        instituteId: user.institute_id,
        tokenId: tokenId
      },
      'your-jwt-secret-key-here',
      { expiresIn: '24h' }
    )

    // Extract session information
    const userAgent = req.headers['user-agent'] || ''
    const ipAddress = req.ip || req.connection.remoteAddress || req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                     req.headers['x-forwarded-for']?.split(',')[0] || 'unknown'

    // Parse user agent for device/browser info
    const parseUserAgent = (ua) => {
      const isMobile = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua)
      const isBot = /bot|crawler|spider|crawling/i.test(ua)

      // Extract browser info
      let browserName = 'Unknown'
      let browserVersion = 'Unknown'
      if (ua.includes('Chrome')) {
        browserName = 'Chrome'
        const match = ua.match(/Chrome\/([0-9.]+)/)
        browserVersion = match ? match[1] : 'Unknown'
      } else if (ua.includes('Firefox')) {
        browserName = 'Firefox'
        const match = ua.match(/Firefox\/([0-9.]+)/)
        browserVersion = match ? match[1] : 'Unknown'
      } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
        browserName = 'Safari'
        const match = ua.match(/Version\/([0-9.]+)/)
        browserVersion = match ? match[1] : 'Unknown'
      } else if (ua.includes('Edge')) {
        browserName = 'Edge'
        const match = ua.match(/Edge\/([0-9.]+)/)
        browserVersion = match ? match[1] : 'Unknown'
      }

      // Extract OS info
      let operatingSystem = 'Unknown'
      if (ua.includes('Windows NT')) {
        operatingSystem = 'Windows'
      } else if (ua.includes('Mac OS X')) {
        operatingSystem = 'macOS'
      } else if (ua.includes('Linux')) {
        operatingSystem = 'Linux'
      } else if (ua.includes('Android')) {
        operatingSystem = 'Android'
      } else if (ua.includes('iOS') || ua.includes('iPhone') || ua.includes('iPad')) {
        operatingSystem = 'iOS'
      }

      return {
        isMobile,
        isBot,
        browserName,
        browserVersion,
        operatingSystem
      }
    }

    const deviceInfo = parseUserAgent(userAgent)

    // Create session record
    const sessionId = `session_${user.id}_${Date.now()}`
    const loginTime = new Date()
    const expiresAt = new Date(loginTime.getTime() + 24 * 60 * 60 * 1000) // 24 hours

    try {
      console.log(`🔄 Creating session for user ${user.id}...`)
      console.log(`📝 Session data:`, {
        userId: user.id,
        sessionId,
        tokenId,
        deviceType: deviceInfo.isMobile ? 'mobile' : 'desktop',
        browserName: deviceInfo.browserName,
        operatingSystem: deviceInfo.operatingSystem,
        ipAddress,
        isMobile: deviceInfo.isMobile
      })

      // Insert session data into user_sessions table
      const result = await db.execute(`
        INSERT INTO user_sessions (
          user_id, session_id, token_id, token_type,
          device_type, browser_name, browser_version, operating_system,
          user_agent, ip_address, last_activity_at, expires_at,
          status, is_current_session, is_mobile, is_bot
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        user.id,
        sessionId,
        tokenId,
        'access',
        deviceInfo.isMobile ? 'mobile' : 'desktop',
        deviceInfo.browserName,
        deviceInfo.browserVersion,
        deviceInfo.operatingSystem,
        userAgent,
        ipAddress,
        loginTime,
        expiresAt,
        'active',
        1, // is_current_session (use 1 instead of true)
        deviceInfo.isMobile ? 1 : 0,
        deviceInfo.isBot ? 1 : 0
      ])

      console.log(`✅ Session insert result:`, result[0])

      // Update user's last login time
      await db.execute(
        'UPDATE users SET last_login_at = NOW() WHERE id = ?',
        [user.id]
      )

      console.log(`✅ Session created for user ${user.id}: ${sessionId}`)

    } catch (sessionError) {
      console.error('❌ Failed to create session record:', sessionError.message)
      console.error('❌ Full error:', sessionError)
      // Continue with login even if session creation fails
    }

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          instituteId: user.institute_id,
          firstName: user.first_name,
          lastName: user.last_name,
          email: user.email,
          role: user.role,
          status: user.status
        },
        institute: {
          id: user.institute_id,
          name: user.institute_name,
          status: user.institute_status
        },
        tokens: {
          accessToken: token,
          tokenType: 'Bearer',
          expiresIn: '24h'
        }
      }
    })

  } catch (error) {
    console.error('Login error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    })
  }
})

/**
 * @swagger
 * /api/v1/auth/me:
 *   get:
 *     tags:
 *       - Authentication
 *     summary: Get current user profile
 *     description: Get the profile of the currently authenticated user
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Unauthorized
 */
app.get('/api/v1/auth/me', authenticateToken, async (req, res) => {
  try {
    const [users] = await db.execute(
      `SELECT u.*, i.name as institute_name, i.status as institute_status, i.subscription_plan, i.subscription_status
       FROM users u
       JOIN institutes i ON u.institute_id = i.id
       WHERE u.id = ?`,
      [req.user.userId]
    )

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    const user = users[0]

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          instituteId: user.institute_id,
          firstName: user.first_name,
          lastName: user.last_name,
          email: user.email,
          role: user.role,
          status: user.status,
          createdAt: user.created_at
        },
        institute: {
          id: user.institute_id,
          name: user.institute_name,
          status: user.institute_status,
          subscriptionPlan: user.subscription_plan,
          subscriptionStatus: user.subscription_status
        }
      }
    })

  } catch (error) {
    console.error('Get user profile error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    })
  }
})

/**
 * @swagger
 * /api/v1/auth/logout:
 *   post:
 *     tags:
 *       - Authentication
 *     summary: Logout user
 *     description: Logout the current user and invalidate session
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 */
app.post('/api/v1/auth/logout', authenticateToken, async (req, res) => {
  try {
    // Get user info from token
    const userId = req.user.userId
    const tokenId = req.user.tokenId

    // Mark specific session as logged out using token_id
    try {
      if (tokenId) {
        await db.execute(
          'UPDATE user_sessions SET status = "logged_out", logout_at = NOW(), is_current_session = FALSE WHERE user_id = ? AND token_id = ? AND status = "active"',
          [userId, tokenId]
        )
        console.log(`✅ Session logged out for user ${userId}, token: ${tokenId}`)
      } else {
        // Fallback: mark all active sessions as logged out
        await db.execute(
          'UPDATE user_sessions SET status = "logged_out", logout_at = NOW(), is_current_session = FALSE WHERE user_id = ? AND status = "active"',
          [userId]
        )
        console.log(`✅ All sessions logged out for user ${userId}`)
      }
    } catch (sessionError) {
      console.error('❌ Session logout error:', sessionError.message)
      // Continue with logout even if session update fails
    }

    res.json({
      success: true,
      message: 'Logout successful. You have been securely logged out.'
    })

  } catch (error) {
    console.error('Logout error:', error)
    res.status(500).json({
      success: false,
      message: 'Logout failed. Please try again.',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    })
  }
})

/**
 * @swagger
 * /api/v1/institutes/current:
 *   get:
 *     tags:
 *       - Institute Management
 *     summary: Get current user's institute details
 *     description: Get detailed information about the current user's institute
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Institute details retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Institute not found
 */
app.get('/api/v1/institutes/current', authenticateToken, async (req, res) => {
  try {
    const [institutes] = await db.execute(
      'SELECT * FROM institutes WHERE id = ?',
      [req.user.instituteId]
    )

    if (institutes.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Institute not found'
      })
    }

    const institute = institutes[0]

    // Get institute statistics
    const [userCount] = await db.execute(
      'SELECT COUNT(*) as total FROM users WHERE institute_id = ?',
      [req.user.instituteId]
    )

    const [adminCount] = await db.execute(
      'SELECT COUNT(*) as total FROM users WHERE institute_id = ? AND role IN ("institute_admin", "institute_staff")',
      [req.user.instituteId]
    )

    const [studentCount] = await db.execute(
      'SELECT COUNT(*) as total FROM users WHERE institute_id = ? AND role = "student"',
      [req.user.instituteId]
    )

    res.json({
      success: true,
      data: {
        institute: {
          id: institute.id,
          name: institute.name,
          code: institute.code,
          slug: institute.slug,
          email: institute.email,
          status: institute.status,
          subscriptionPlan: institute.subscription_plan,
          subscriptionStatus: institute.subscription_status,
          timezone: institute.timezone,
          currency: institute.currency,
          language: institute.language,
          maxStudents: institute.max_students,
          maxStaff: institute.max_staff,
          maxCourses: institute.max_courses,
          createdAt: institute.created_at,
          updatedAt: institute.updated_at
        },
        statistics: {
          totalUsers: userCount[0].total,
          totalAdmins: adminCount[0].total,
          totalStudents: studentCount[0].total
        }
      }
    })

  } catch (error) {
    console.error('Get institute details error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    })
  }
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.originalUrl
  })
})

// Error handling
app.use((err, _req, res, _next) => {
  console.error(err.stack)
  res.status(500).json({
    success: false,
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  })
})

app.listen(PORT, () => {
  console.log(`🚀 LMS Backend server is running on http://localhost:${PORT}`)
  console.log(`📚 API Documentation: http://localhost:${PORT}/docs`)
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`)
})
