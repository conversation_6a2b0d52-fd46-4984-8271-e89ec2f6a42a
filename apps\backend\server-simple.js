import express from 'express'
import cors from 'cors'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import mysql from 'mysql2/promise'
import swaggerJSDoc from 'swagger-jsdoc'
import swaggerUi from 'swagger-ui-express'

const app = express()
const PORT = process.env.PORT || 3333

// Database connection
const dbConfig = {
  host: '127.0.0.1',
  port: 3307,
  user: 'root',
  password: '',
  database: 'lms'
}

let db
try {
  db = await mysql.createConnection(dbConfig)
  console.log('✅ Connected to MySQL database')
} catch (error) {
  console.error('❌ Database connection failed:', error.message)
  process.exit(1)
}

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'LMS Platform API',
      version: '1.0.0',
      description: 'A comprehensive Learning Management System API',
    },
    servers: [
      {
        url: 'http://localhost:3333',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    }
  },
  apis: ['./server-simple.js']
}

const swaggerSpec = swaggerJSDoc(swaggerOptions)

// Swagger UI
app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec))

// Basic routes
app.get('/', (req, res) => {
  res.json({
    message: 'LMS Backend API is running!',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    documentation: 'http://localhost:3333/docs'
  })
})

app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  })
})

/**
 * @swagger
 * /api/v1/auth/institute/register:
 *   post:
 *     tags:
 *       - Institute Admin Authentication
 *     summary: Institute Registration
 *     description: Register a new institute with admin user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - instituteName
 *               - firstName
 *               - lastName
 *               - email
 *               - password
 *               - confirmPassword
 *             properties:
 *               instituteName:
 *                 type: string
 *                 example: "Demo University"
 *               firstName:
 *                 type: string
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 example: "Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 8
 *                 example: "admin123"
 *               confirmPassword:
 *                 type: string
 *                 format: password
 *                 example: "admin123"
 *     responses:
 *       201:
 *         description: Institute registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Institute registered successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     institute:
 *                       type: object
 *                     user:
 *                       type: object
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
app.post('/api/v1/auth/institute/register', async (req, res) => {
  try {
    const { instituteName, firstName, lastName, email, password, confirmPassword } = req.body

    // Validation
    if (!instituteName || !firstName || !lastName || !email || !password || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required',
        errors: {
          instituteName: !instituteName ? ['Institute name is required'] : undefined,
          firstName: !firstName ? ['First name is required'] : undefined,
          lastName: !lastName ? ['Last name is required'] : undefined,
          email: !email ? ['Email is required'] : undefined,
          password: !password ? ['Password is required'] : undefined,
          confirmPassword: !confirmPassword ? ['Confirm password is required'] : undefined,
        }
      })
    }

    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Passwords do not match',
        errors: {
          confirmPassword: ['Passwords do not match']
        }
      })
    }

    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long',
        errors: {
          password: ['Password must be at least 8 characters long']
        }
      })
    }

    // Check if email already exists
    const [existingUsers] = await db.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    )

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Email already exists',
        errors: {
          email: ['Email already exists']
        }
      })
    }

    // Check if institute name already exists
    const [existingInstitutes] = await db.execute(
      'SELECT id FROM institutes WHERE name = ?',
      [instituteName]
    )

    if (existingInstitutes.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Institute name already exists',
        errors: {
          instituteName: ['Institute name already exists']
        }
      })
    }

    // Create institute
    const slug = instituteName.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-')
    const [instituteResult] = await db.execute(
      `INSERT INTO institutes (name, code, slug, email, status, subscription_plan, subscription_status, 
       timezone, currency, language, max_students, max_staff, max_courses, created_at, updated_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        instituteName,
        slug.toUpperCase(),
        slug,
        email,
        'pending',
        'basic',
        'trial',
        'UTC',
        'USD',
        'en',
        100,
        10,
        50
      ]
    )

    const instituteId = instituteResult.insertId

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const [userResult] = await db.execute(
      `INSERT INTO users (institute_id, first_name, last_name, email, password, role, status, 
       created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [instituteId, firstName, lastName, email, hashedPassword, 'institute_admin', 'pending']
    )

    const userId = userResult.insertId

    // Get created institute and user
    const [institutes] = await db.execute('SELECT * FROM institutes WHERE id = ?', [instituteId])
    const [users] = await db.execute('SELECT id, institute_id, first_name, last_name, email, role, status, created_at FROM users WHERE id = ?', [userId])

    const institute = institutes[0]
    const user = users[0]

    res.status(201).json({
      success: true,
      message: 'Institute registered successfully. Please wait for approval.',
      data: {
        institute: {
          id: institute.id,
          name: institute.name,
          code: institute.code,
          slug: institute.slug,
          email: institute.email,
          status: institute.status,
          subscriptionPlan: institute.subscription_plan,
          subscriptionStatus: institute.subscription_status,
          createdAt: institute.created_at
        },
        user: {
          id: user.id,
          instituteId: user.institute_id,
          firstName: user.first_name,
          lastName: user.last_name,
          email: user.email,
          role: user.role,
          status: user.status,
          createdAt: user.created_at
        }
      }
    })

  } catch (error) {
    console.error('Institute registration error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    })
  }
})

/**
 * @swagger
 * /api/v1/auth/institute/login:
 *   post:
 *     tags:
 *       - Institute Admin Authentication
 *     summary: Institute Admin Login
 *     description: Authenticate institute admin or staff users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 example: "admin123"
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 *       500:
 *         description: Internal server error
 */
app.post('/api/v1/auth/institute/login', async (req, res) => {
  try {
    const { email, password } = req.body

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      })
    }

    // Find user with institute
    const [users] = await db.execute(
      `SELECT u.*, i.name as institute_name, i.status as institute_status 
       FROM users u 
       JOIN institutes i ON u.institute_id = i.id 
       WHERE u.email = ? AND u.role IN ('institute_admin', 'institute_staff')`,
      [email]
    )

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      })
    }

    const user = users[0]

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      })
    }

    // Check if institute is active
    if (user.institute_status !== 'active') {
      return res.status(403).json({
        success: false,
        message: 'Institute is not active. Please contact support.'
      })
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        role: user.role,
        instituteId: user.institute_id 
      },
      'your-jwt-secret-key-here',
      { expiresIn: '24h' }
    )

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          instituteId: user.institute_id,
          firstName: user.first_name,
          lastName: user.last_name,
          email: user.email,
          role: user.role,
          status: user.status
        },
        institute: {
          id: user.institute_id,
          name: user.institute_name,
          status: user.institute_status
        },
        tokens: {
          accessToken: token,
          tokenType: 'Bearer',
          expiresIn: '24h'
        }
      }
    })

  } catch (error) {
    console.error('Login error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    })
  }
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.originalUrl
  })
})

// Error handling
app.use((err, _req, res, _next) => {
  console.error(err.stack)
  res.status(500).json({
    success: false,
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  })
})

app.listen(PORT, () => {
  console.log(`🚀 LMS Backend server is running on http://localhost:${PORT}`)
  console.log(`📚 API Documentation: http://localhost:${PORT}/docs`)
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`)
})
