import { BaseSeeder } from '@adonisjs/lucid/seeders'
import Permission from '#models/permission'
import Module from '#models/module'

export default class extends BaseSeeder {
  async run() {
    // Get module IDs
    const usersModule = await Module.findByOrFail('name', 'users')
    const coursesModule = await Module.findByOrFail('name', 'courses')
    const studentsModule = await Module.findByOrFail('name', 'students')
    const instructorsModule = await Module.findByOrFail('name', 'instructors')
    const financeModule = await Module.findByOrFail('name', 'finance')
    const reportsModule = await Module.findByOrFail('name', 'reports')
    const settingsModule = await Module.findByOrFail('name', 'settings')

    // Users module permissions
    await Permission.createMany([
      {
        name: 'users.view',
        display_name: 'View Users',
        description: 'Can view user list and details',
        module_id: usersModule.id
      },
      {
        name: 'users.create',
        display_name: 'Create Users',
        description: 'Can create new users',
        module_id: usersModule.id
      },
      {
        name: 'users.edit',
        display_name: 'Edit Users',
        description: 'Can edit existing users',
        module_id: usersModule.id
      },
      {
        name: 'users.delete',
        display_name: 'Delete Users',
        description: 'Can delete users',
        module_id: usersModule.id
      },
      {
        name: 'roles.view',
        display_name: 'View Roles',
        description: 'Can view roles and permissions',
        module_id: usersModule.id
      },
      {
        name: 'roles.assign',
        display_name: 'Assign Roles',
        description: 'Can assign roles to users',
        module_id: usersModule.id
      }
    ])

    // Courses module permissions
    await Permission.createMany([
      {
        name: 'courses.view',
        display_name: 'View Courses',
        description: 'Can view course list and details',
        module_id: coursesModule.id
      },
      {
        name: 'courses.create',
        display_name: 'Create Courses',
        description: 'Can create new courses',
        module_id: coursesModule.id
      },
      {
        name: 'courses.edit',
        display_name: 'Edit Courses',
        description: 'Can edit existing courses',
        module_id: coursesModule.id
      },
      {
        name: 'courses.delete',
        display_name: 'Delete Courses',
        description: 'Can delete courses',
        module_id: coursesModule.id
      }
    ])

    // Students module permissions
    await Permission.createMany([
      {
        name: 'students.view',
        display_name: 'View Students',
        description: 'Can view student list and details',
        module_id: studentsModule.id
      },
      {
        name: 'students.create',
        display_name: 'Create Students',
        description: 'Can create new student accounts',
        module_id: studentsModule.id
      },
      {
        name: 'students.edit',
        display_name: 'Edit Students',
        description: 'Can edit student information',
        module_id: studentsModule.id
      },
      {
        name: 'enrollments.manage',
        display_name: 'Manage Enrollments',
        description: 'Can manage student enrollments',
        module_id: studentsModule.id
      }
    ])

    // Add more permissions for other modules...
  }
}