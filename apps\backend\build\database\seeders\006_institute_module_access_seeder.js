import { BaseSeeder } from '@adonisjs/lucid/seeders';
import InstituteModuleAccess from '#models/institute_module_access';
import Institute from '#models/institute';
import Module from '#models/module';
export default class extends BaseSeeder {
    async run() {
        const demoInstitute = await Institute.findByOrFail('slug', 'demo-institute');
        const harvard = await Institute.findByOrFail('slug', 'harvard-university');
        const mit = await Institute.findByOrFail('slug', 'mit');
        const modules = await Module.all();
        for (const module of modules) {
            await InstituteModuleAccess.create({
                institute_id: harvard.id,
                module_id: module.id,
                is_active: true
            });
            await InstituteModuleAccess.create({
                institute_id: mit.id,
                module_id: module.id,
                is_active: true
            });
        }
        const basicModules = await Module.query()
            .whereIn('name', ['users', 'courses', 'students', 'instructors']);
        for (const module of basicModules) {
            await InstituteModuleAccess.create({
                institute_id: demoInstitute.id,
                module_id: module.id,
                is_active: true
            });
        }
    }
}
//# sourceMappingURL=006_institute_module_access_seeder.js.map