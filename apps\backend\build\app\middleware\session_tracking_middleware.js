import SessionService from '#services/session_service';
export default class SessionTrackingMiddleware {
    sessionService = new SessionService();
    async handle(ctx, next) {
        if (ctx.auth.user) {
            try {
                const sessionId = this.extractSessionId(ctx);
                if (sessionId) {
                    await this.sessionService.updateSessionActivity(sessionId);
                }
            }
            catch (error) {
                console.error('Session tracking error:', error.message);
            }
        }
        await next();
    }
    extractSessionId(ctx) {
        const token = ctx.request.header('authorization')?.replace('Bearer ', '');
        if (token) {
            try {
            }
            catch (error) {
            }
        }
        const sessionId = ctx.request.header('x-session-id');
        if (sessionId) {
            return sessionId;
        }
        const sessionCookie = ctx.request.cookie('session_id');
        if (sessionCookie) {
            return sessionCookie;
        }
        return null;
    }
}
//# sourceMappingURL=session_tracking_middleware.js.map