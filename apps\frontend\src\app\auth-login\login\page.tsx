"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAppDispatch, useAppSelector } from '@/store'
import { loginInstitute, clearError } from '@/store/slices/authSlice'
import { Building2, Eye, EyeOff, Loader2, Shield, Mail, Users, BookOpen, TrendingUp } from 'lucide-react'

export default function InstituteAdminLoginPage() {
  const dispatch = useAppDispatch()
  const { isLoading, error, isAuthenticated } = useAppSelector((state) => state.auth)
  const router = useRouter()

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/institute-admin')
    }
  }, [isAuthenticated, router])

  // Clear error when component mounts
  useEffect(() => {
    dispatch(clearError())
  }, [dispatch])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const result = await dispatch(loginInstitute({ email, password }))

      if (loginInstitute.fulfilled.match(result)) {
        // Login successful, redirect to dashboard
        router.push('/institute-admin')
      }
    } catch (err) {
      // Error is handled by Redux
      console.error('Login failed:', err)
    }
  }

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
      <div className="flex h-screen">
        {/* Left Side - Marketing Content */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-green-600 via-emerald-600 to-teal-700 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

          {/* Content */}
          <div className="relative z-10 flex flex-col justify-center px-8 py-8 text-white">
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl mb-4">
                <Building2 className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-3xl font-bold mb-3 leading-tight">
                Institute Administration Portal
              </h1>
              <p className="text-lg text-green-100 mb-6 leading-relaxed">
                Manage your educational institution with powerful administrative tools and comprehensive analytics.
              </p>
            </div>

            {/* Features - Compact */}
            <div className="grid grid-cols-1 gap-3 mb-6">
              <div className="flex items-center space-x-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg">
                <div className="flex-shrink-0">
                  <Users className="h-5 w-5 text-green-200" />
                </div>
                <div>
                  <h3 className="font-medium text-white text-sm">Student Management</h3>
                  <p className="text-xs text-green-100">Manage enrollments, track progress, and handle student data</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg">
                <div className="flex-shrink-0">
                  <BookOpen className="h-5 w-5 text-green-200" />
                </div>
                <div>
                  <h3 className="font-medium text-white text-sm">Course Administration</h3>
                  <p className="text-xs text-green-100">Create courses, assign instructors, and manage curriculum</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-5 w-5 text-green-200" />
                </div>
                <div>
                  <h3 className="font-medium text-white text-sm">Analytics & Reports</h3>
                  <p className="text-xs text-green-100">Comprehensive insights and performance analytics</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg">
                <div className="flex-shrink-0">
                  <Shield className="h-5 w-5 text-green-200" />
                </div>
                <div>
                  <h3 className="font-medium text-white text-sm">Security & Compliance</h3>
                  <p className="text-xs text-green-100">Advanced security features and compliance tools</p>
                </div>
              </div>
            </div>

            {/* Stats - Compact */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">500+</div>
                <div className="text-xs text-green-200">Institutes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">50K+</div>
                <div className="text-xs text-green-200">Students</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">2K+</div>
                <div className="text-xs text-green-200">Courses</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-4 lg:p-6">
          <div className="w-full max-w-sm">
            {/* Header */}
            <div className="text-center mb-6">
              <div className="lg:hidden inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl mb-3">
                <Building2 className="h-6 w-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
                Institute Login
              </h2>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                Access your institute administration panel
              </p>
            </div>

            {/* Login Form */}
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm dark:bg-slate-800/80">
              <CardContent className="p-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  {error && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg text-sm flex items-center">
                      <Shield className="h-4 w-4 mr-2 flex-shrink-0" />
                      {error}
                    </div>
                  )}

                  <div className="space-y-1">
                    <Label htmlFor="email" className="text-xs font-medium text-slate-700 dark:text-slate-300">
                      Email Address *
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="pl-9 h-10 text-sm border-slate-300 focus:border-green-500 focus:ring-green-500"
                      />
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor="password" className="text-xs font-medium text-slate-700 dark:text-slate-300">
                      Password *
                    </Label>
                    <div className="relative">
                      <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        className="pl-9 pr-10 h-10 text-sm border-slate-300 focus:border-green-500 focus:ring-green-500"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-10 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium text-sm rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Signing in...
                      </>
                    ) : (
                      'Sign In'
                    )}
                  </Button>
                </form>

                <div className="mt-6 text-center">
                  <p className="text-xs text-slate-600 dark:text-slate-400">
                    Don't have an account?{' '}
                    <Link
                      href="/auth-login/register"
                      className="font-medium text-green-600 hover:text-green-500 dark:text-green-400 transition-colors"
                    >
                      Register your institute
                    </Link>
                  </p>

                  <div className="mt-3">
                    <p className="text-xs text-slate-600 dark:text-slate-400">
                      Need access to super admin portal?{' '}
                      <Link
                        href="/auth/login"
                        className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 transition-colors"
                      >
                        Sign in here
                      </Link>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Demo Credentials */}
            <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <h3 className="text-xs font-semibold text-green-800 dark:text-green-200 mb-2">
                Demo Credentials
              </h3>
              <div className="text-xs text-green-700 dark:text-green-300 space-y-1">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> admin123</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
