paths:
  # Student Authentication
  /auth/student/login:
    post:
      tags:
        - Student Authentication
      summary: Student Login
      description: Authenticate student users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  example: "student123"
      responses:
        200:
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Login successful"
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      institute:
                        $ref: '#/components/schemas/Institute'
                      enrollments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            course:
                              $ref: '#/components/schemas/Course'
                            status:
                              type: string
                              enum: [enrolled, completed, dropped, suspended]
                            progress:
                              type: number
                              example: 75.5
                            grade:
                              type: string
                              example: "A"
                      tokens:
                        $ref: '#/components/schemas/AuthTokens'
                      permissions:
                        type: array
                        items:
                          type: string
                        example: ["courses.view", "assignments.submit", "grades.view"]
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          description: Institute not active
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        422:
          $ref: '#/components/responses/ValidationError'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/student/refresh:
    post:
      tags:
        - Student Authentication
      summary: Refresh Access Token
      description: Refresh the access token using refresh token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refresh_token
              properties:
                refresh_token:
                  type: string
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      responses:
        200:
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Token refreshed successfully"
                  data:
                    type: object
                    properties:
                      tokens:
                        $ref: '#/components/schemas/AuthTokens'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/student/me:
    get:
      tags:
        - Student Authentication
      summary: Get Current User
      description: Get current authenticated student user information
      security:
        - bearerAuth: []
      responses:
        200:
          description: User information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      institute:
                        $ref: '#/components/schemas/Institute'
                      enrollments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            course:
                              $ref: '#/components/schemas/Course'
                            status:
                              type: string
                            progress:
                              type: number
                            grade:
                              type: string
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/student/dashboard:
    get:
      tags:
        - Student Authentication
      summary: Get Dashboard Data
      description: Get student dashboard with courses, assignments, and progress
      security:
        - bearerAuth: []
      responses:
        200:
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      stats:
                        type: object
                        properties:
                          totalCourses:
                            type: integer
                            example: 5
                          completedCourses:
                            type: integer
                            example: 2
                          overallProgress:
                            type: number
                            example: 68.5
                          averageGrade:
                            type: number
                            example: 85.2
                      enrollments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            course:
                              $ref: '#/components/schemas/Course'
                            status:
                              type: string
                            progress:
                              type: number
                            grade:
                              type: string
                      pendingAssignments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            title:
                              type: string
                            course:
                              type: object
                              properties:
                                id:
                                  type: integer
                                title:
                                  type: string
                            dueDate:
                              type: string
                              format: date-time
                      recentGrades:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            assignment:
                              type: string
                            course:
                              type: string
                            grade:
                              type: string
                            gradedAt:
                              type: string
                              format: date-time
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/student/logout:
    post:
      tags:
        - Student Authentication
      summary: Logout
      description: Logout and invalidate current session
      security:
        - bearerAuth: []
      responses:
        200:
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Logout successful"
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  # Course Access
  /student/courses:
    get:
      tags:
        - Student - Courses
      summary: List Available Courses
      description: Get list of courses available to the student
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [enrolled, available, completed]
      responses:
        200:
          description: Courses retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      courses:
                        type: array
                        items:
                          $ref: '#/components/schemas/Course'
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  # Assignments
  /student/assignments:
    get:
      tags:
        - Student - Assignments
      summary: List Assignments
      description: Get list of assignments for the student
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, submitted, graded]
        - name: course_id
          in: query
          schema:
            type: integer
      responses:
        200:
          description: Assignments retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      assignments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            title:
                              type: string
                            description:
                              type: string
                            course:
                              type: object
                              properties:
                                id:
                                  type: integer
                                title:
                                  type: string
                            dueDate:
                              type: string
                              format: date-time
                            status:
                              type: string
                              enum: [pending, submitted, graded]
                            grade:
                              type: string
                            submittedAt:
                              type: string
                              format: date-time
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  # Grades
  /student/grades:
    get:
      tags:
        - Student - Grades
      summary: List Grades
      description: Get list of grades for the student
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: course_id
          in: query
          schema:
            type: integer
      responses:
        200:
          description: Grades retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      grades:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            assignment:
                              type: string
                            course:
                              type: object
                              properties:
                                id:
                                  type: integer
                                title:
                                  type: string
                            grade:
                              type: string
                              example: "A"
                            points:
                              type: number
                              example: 95
                            maxPoints:
                              type: number
                              example: 100
                            gradedAt:
                              type: string
                              format: date-time
                            feedback:
                              type: string
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /student/grades/transcript:
    get:
      tags:
        - Student - Grades
      summary: Get Transcript
      description: Get complete academic transcript for the student
      security:
        - bearerAuth: []
      responses:
        200:
          description: Transcript retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      student:
                        $ref: '#/components/schemas/User'
                      institute:
                        $ref: '#/components/schemas/Institute'
                      overallGPA:
                        type: number
                        example: 3.75
                      totalCredits:
                        type: number
                        example: 120
                      courses:
                        type: array
                        items:
                          type: object
                          properties:
                            course:
                              $ref: '#/components/schemas/Course'
                            grade:
                              type: string
                              example: "A"
                            credits:
                              type: number
                              example: 3
                            semester:
                              type: string
                              example: "Fall 2024"
                            completedAt:
                              type: string
                              format: date-time
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'