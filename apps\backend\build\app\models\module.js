var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm';
import Permission from './permission.js';
import InstituteModuleAccess from './institute_module_access.js';
import RoleModulePermission from './role_module_permission.js';
export default class Module extends BaseModel {
    get isEnabled() {
        return this.isActive;
    }
    serialize() {
        return {
            id: this.id,
            name: this.name,
            displayName: this.displayName,
            description: this.description,
            isActive: this.isActive,
            isEnabled: this.isEnabled,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], Module.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Module.prototype, "name", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Module.prototype, "displayName", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Module.prototype, "description", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], Module.prototype, "isActive", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], Module.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], Module.prototype, "updatedAt", void 0);
__decorate([
    hasMany(() => Permission),
    __metadata("design:type", Object)
], Module.prototype, "permissions", void 0);
__decorate([
    hasMany(() => InstituteModuleAccess),
    __metadata("design:type", Object)
], Module.prototype, "instituteAccess", void 0);
__decorate([
    hasMany(() => RoleModulePermission),
    __metadata("design:type", Object)
], Module.prototype, "rolePermissions", void 0);
//# sourceMappingURL=module.js.map