import { DateTime } from 'luxon'
import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm'
import type { HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import Branch from './branch.js'
import Course from './course.js'
import InstituteModuleAccess from './institute_module_access.js'
import RoleModulePermission from './role_module_permission.js'
import UserRole from './user_role.js'

export default class Institute extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare code: string

  @column()
  declare slug: string

  @column()
  declare description: string | null

  @column()
  declare logo: string | null

  @column()
  declare banner: string | null

  @column()
  declare website: string | null

  @column()
  declare email: string

  @column()
  declare phone: string | null

  @column()
  declare address: string | null

  @column()
  declare city: string | null

  @column()
  declare state: string | null

  @column()
  declare country: string | null

  @column()
  declare zipCode: string | null

  @column()
  declare timezone: string

  @column()
  declare currency: string

  @column()
  declare language: string

  @column()
  declare status: 'active' | 'inactive' | 'suspended' | 'pending'

  @column()
  declare subscriptionPlan: 'basic' | 'professional' | 'enterprise'

  @column()
  declare subscriptionStatus: 'active' | 'inactive' | 'trial' | 'expired'

  @column.dateTime()
  declare subscriptionExpiresAt: DateTime | null

  @column()
  declare maxStudents: number

  @column()
  declare maxStaff: number

  @column()
  declare maxCourses: number

  @column()
  declare customDomain: string | null

  @column()
  declare subdomain: string | null

  @column()
  declare theme: Record<string, any> | null

  @column()
  declare settings: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  /**
   * Relationships
   */
  @hasMany(() => User)
  declare users: HasMany<typeof User>

  @hasMany(() => Branch)
  declare branches: HasMany<typeof Branch>

  @hasMany(() => Course)
  declare courses: HasMany<typeof Course>

  @hasMany(() => InstituteModuleAccess)
  declare moduleAccess: HasMany<typeof InstituteModuleAccess>

  @hasMany(() => RoleModulePermission)
  declare rolePermissions: HasMany<typeof RoleModulePermission>

  @hasMany(() => UserRole)
  declare userRoles: HasMany<typeof UserRole>

  /**
   * Computed properties
   */
  get isActive() {
    return this.status === 'active'
  }

  get isSubscriptionActive() {
    return this.subscriptionStatus === 'active' || this.subscriptionStatus === 'trial'
  }

  get isSubscriptionExpired() {
    if (!this.subscriptionExpiresAt) return false
    return this.subscriptionExpiresAt < DateTime.now()
  }

  get domain() {
    if (this.customDomain) return this.customDomain
    if (this.subdomain) return `${this.subdomain}.lms-platform.com`
    return null
  }

  /**
   * Serialize institute data for API responses
   */
  serialize() {
    return {
      id: this.id,
      name: this.name,
      code: this.code,
      slug: this.slug,
      description: this.description,
      logo: this.logo,
      banner: this.banner,
      website: this.website,
      email: this.email,
      phone: this.phone,
      address: this.address,
      city: this.city,
      state: this.state,
      country: this.country,
      zipCode: this.zipCode,
      timezone: this.timezone,
      currency: this.currency,
      language: this.language,
      status: this.status,
      subscriptionPlan: this.subscriptionPlan,
      subscriptionStatus: this.subscriptionStatus,
      subscriptionExpiresAt: this.subscriptionExpiresAt,
      maxStudents: this.maxStudents,
      maxStaff: this.maxStaff,
      maxCourses: this.maxCourses,
      customDomain: this.customDomain,
      subdomain: this.subdomain,
      domain: this.domain,
      theme: this.theme,
      settings: this.settings,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
