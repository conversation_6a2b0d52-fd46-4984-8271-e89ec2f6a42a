{"version": 3, "file": "005_create_enrollments_table.js", "sourceRoot": "", "sources": ["../../../database/migrations/005_create_enrollments_table.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAA;AAEnD,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IAC3B,SAAS,GAAG,aAAa,CAAA;IAEnC,KAAK,CAAC,EAAE;QACN,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAChD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAA;YAGhC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAA;YACjD,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAE9E,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAA;YACnD,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAGlF,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;YAC7F,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YAC5C,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGpC,KAAK,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC3D,KAAK,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YACrE,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YACpE,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YAGpE,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;YACxB,KAAK,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;YAC1B,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;YACvB,KAAK,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAA;YAC5B,KAAK,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAA;QACxC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACvC,CAAC;CACF"}