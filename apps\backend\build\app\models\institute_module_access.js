var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm';
import Institute from './institute.js';
import Module from './module.js';
export default class InstituteModuleAccess extends BaseModel {
    get isEnabled() {
        return this.isActive;
    }
    get isDisabled() {
        return !this.isActive;
    }
    serialize() {
        return {
            id: this.id,
            instituteId: this.instituteId,
            moduleId: this.moduleId,
            isActive: this.isActive,
            isEnabled: this.isEnabled,
            isDisabled: this.isDisabled,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], InstituteModuleAccess.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], InstituteModuleAccess.prototype, "instituteId", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], InstituteModuleAccess.prototype, "moduleId", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], InstituteModuleAccess.prototype, "isActive", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], InstituteModuleAccess.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], InstituteModuleAccess.prototype, "updatedAt", void 0);
__decorate([
    belongsTo(() => Institute),
    __metadata("design:type", Object)
], InstituteModuleAccess.prototype, "institute", void 0);
__decorate([
    belongsTo(() => Module),
    __metadata("design:type", Object)
], InstituteModuleAccess.prototype, "module", void 0);
//# sourceMappingURL=institute_module_access.js.map