import { BaseCommand } from '@adonisjs/core/ace';
import { DateTime } from 'luxon';
import UserSession from '#models/user_session';
import SessionService from '#services/session_service';
export default class CleanupSessions extends BaseCommand {
    static commandName = 'sessions:cleanup';
    static description = 'Clean up expired and old user sessions';
    static options = {
        startApp: true,
        allowUnknownFlags: false,
        staysAlive: false,
    };
    async run() {
        this.logger.info('Starting session cleanup...');
        const sessionService = new SessionService();
        try {
            const expiredCount = await sessionService.cleanupExpiredSessions();
            this.logger.info(`Marked ${expiredCount} expired sessions`);
            const oldSessionsCount = await this.deleteOldSessions();
            this.logger.info(`Deleted ${oldSessionsCount} old sessions`);
            const revokedSessionsCount = await this.deleteOldRevokedSessions();
            this.logger.info(`Deleted ${revokedSessionsCount} old revoked sessions`);
            await this.generateCleanupReport();
            this.logger.success('Session cleanup completed successfully');
        }
        catch (error) {
            this.logger.error('Session cleanup failed:', error.message);
            this.exitCode = 1;
        }
    }
    async deleteOldSessions() {
        const cutoffDate = DateTime.now().minus({ days: 90 });
        const result = await UserSession.query()
            .where('createdAt', '<', cutoffDate.toSQL())
            .delete();
        return result[0] || 0;
    }
    async deleteOldRevokedSessions() {
        const cutoffDate = DateTime.now().minus({ days: 30 });
        const result = await UserSession.query()
            .where('status', 'revoked')
            .where('logoutAt', '<', cutoffDate.toSQL())
            .delete();
        return result[0] || 0;
    }
    async generateCleanupReport() {
        const stats = await UserSession.query()
            .select('status')
            .count('* as total')
            .groupBy('status');
        this.logger.info('Current session statistics:');
        for (const stat of stats) {
            this.logger.info(`  ${stat.status}: ${stat.total}`);
        }
        const totalSessions = await UserSession.query().count('* as total');
        const activeSessions = await UserSession.query()
            .where('status', 'active')
            .where('expiresAt', '>', DateTime.now().toSQL())
            .count('* as total');
        this.logger.info(`Total sessions: ${totalSessions[0].total}`);
        this.logger.info(`Active sessions: ${activeSessions[0].total}`);
    }
}
//# sourceMappingURL=cleanup_sessions.js.map