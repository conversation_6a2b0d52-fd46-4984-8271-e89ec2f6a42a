-- Create database if not exists
CREATE DATABASE IF NOT EXISTS lms;
USE lms;

-- <PERSON>reate institutes table
CREATE TABLE IF NOT EXISTS institutes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
  code VA<PERSON>HAR(50) NOT NULL UNIQUE,
  slug VARCHAR(255) NOT NULL UNIQUE,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NULL,
  address TEXT NULL,
  website VARCHAR(255) NULL,
  logo_url VARCHAR(500) NULL,
  favicon_url VARCHAR(500) NULL,
  type ENUM('university', 'college', 'school', 'training_center', 'coaching_institute', 'online_academy', 'corporate_training') DEFAULT 'college',
  affiliation ENUM('government', 'private', 'semi_government', 'autonomous') DEFAULT 'private',
  status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
  subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'basic',
  subscription_status ENUM('trial', 'active', 'expired', 'cancelled') DEFAULT 'trial',
  subscription_expires_at TIMESTAMP NULL,
  country_id INT NULL,
  state_id INT NULL,
  city_id INT NULL,
  timezone VARCHAR(50) DEFAULT 'UTC',
  currency VARCHAR(3) DEFAULT 'USD',
  language VARCHAR(5) DEFAULT 'en',
  max_students INT DEFAULT 100,
  max_staff INT DEFAULT 10,
  max_courses INT DEFAULT 50,
  settings JSON NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_institutes_status (status),
  INDEX idx_institutes_subscription (subscription_plan, subscription_status),
  INDEX idx_institutes_location (country_id, state_id, city_id)
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  institute_id INT NOT NULL,
  branch_id INT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  student_id VARCHAR(50) NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NULL,
  date_of_birth DATE NULL,
  gender ENUM('male', 'female', 'other') NULL,
  address TEXT NULL,
  avatar_url VARCHAR(500) NULL,
  role ENUM('super_admin', 'lms_admin', 'institute_admin', 'institute_staff', 'instructor', 'student') NOT NULL,
  status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
  email_verified_at TIMESTAMP NULL,
  last_login_at TIMESTAMP NULL,
  preferences JSON NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
  INDEX idx_users_email (email),
  INDEX idx_users_role (role),
  INDEX idx_users_status (status),
  INDEX idx_users_institute (institute_id),
  INDEX idx_users_student_id (student_id)
);

-- Create branches table
CREATE TABLE IF NOT EXISTS branches (
  id INT AUTO_INCREMENT PRIMARY KEY,
  institute_id INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  description TEXT NULL,
  address TEXT NULL,
  city VARCHAR(100) NULL,
  state VARCHAR(100) NULL,
  country VARCHAR(100) NULL,
  zip_code VARCHAR(20) NULL,
  phone VARCHAR(20) NULL,
  email VARCHAR(255) NULL,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
  UNIQUE KEY unique_institute_branch_code (institute_id, code),
  INDEX idx_branches_institute (institute_id),
  INDEX idx_branches_status (status)
);

-- Create courses table
CREATE TABLE IF NOT EXISTS courses (
  id INT AUTO_INCREMENT PRIMARY KEY,
  institute_id INT NOT NULL,
  instructor_id INT NULL,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT NULL,
  short_description VARCHAR(500) NULL,
  thumbnail VARCHAR(500) NULL,
  price DECIMAL(10,2) DEFAULT 0.00,
  discount_price DECIMAL(10,2) NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  duration INT NULL COMMENT 'Duration in hours',
  level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
  language VARCHAR(5) DEFAULT 'en',
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  max_students INT NULL,
  enrolled_students INT DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0.00,
  total_ratings INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
  FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL,
  UNIQUE KEY unique_institute_course_slug (institute_id, slug),
  INDEX idx_courses_institute (institute_id),
  INDEX idx_courses_instructor (instructor_id),
  INDEX idx_courses_status (status),
  INDEX idx_courses_level (level)
);

-- Create enrollments table
CREATE TABLE IF NOT EXISTS enrollments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  course_id INT NOT NULL,
  status ENUM('enrolled', 'completed', 'dropped', 'suspended') DEFAULT 'enrolled',
  progress DECIMAL(5,2) DEFAULT 0.00,
  grade VARCHAR(5) NULL,
  enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  dropped_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_course (user_id, course_id),
  INDEX idx_enrollments_user (user_id),
  INDEX idx_enrollments_course (course_id),
  INDEX idx_enrollments_status (status)
);

-- Insert sample data
INSERT IGNORE INTO institutes (name, code, slug, email, status, subscription_plan, subscription_status) VALUES
('Demo University', 'DEMO_UNIV', 'demo-university', '<EMAIL>', 'active', 'premium', 'active'),
('Test College', 'TEST_COL', 'test-college', '<EMAIL>', 'active', 'basic', 'active');

-- Insert sample users
INSERT IGNORE INTO users (institute_id, first_name, last_name, email, password, role, status, email_verified_at) VALUES
(1, 'Super', 'Admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hGzBqxflO', 'super_admin', 'active', NOW()),
(1, 'John', 'Doe', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hGzBqxflO', 'institute_admin', 'active', NOW()),
(1, 'Jane', 'Smith', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hGzBqxflO', 'student', 'active', NOW());

-- Insert sample courses
INSERT IGNORE INTO courses (institute_id, instructor_id, title, slug, description, status, level) VALUES
(1, 2, 'Introduction to Computer Science', 'intro-computer-science', 'A comprehensive introduction to computer science fundamentals', 'published', 'beginner'),
(1, 2, 'Advanced Mathematics', 'advanced-mathematics', 'Advanced mathematical concepts and applications', 'published', 'advanced');

-- Insert sample enrollments
INSERT IGNORE INTO enrollments (user_id, course_id, status, progress) VALUES
(3, 1, 'enrolled', 75.50),
(3, 2, 'enrolled', 45.25);
