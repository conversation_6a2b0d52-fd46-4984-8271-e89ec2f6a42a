import api from '@/lib/api'

export interface RegisterData {
  instituteName: string
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
}

export interface LoginData {
  email: string
  password: string
  remember?: boolean
}

export interface AuthResponse {
  success: boolean
  message: string
  data: {
    user: {
      id: number
      first_name: string
      last_name: string
      email: string
      user_type: string
      status: string
      is_verified: boolean
    }
    institute: {
      id: number
      name: string
      email: string
      status: string
      is_verified: boolean
    }
    tokens: {
      access_token: string
      refresh_token: string
      expires_in: number
      token_type: string
    }
    permissions?: string[]
  }
}

export interface UserResponse {
  success: boolean
  data: {
    user: {
      id: number
      first_name: string
      last_name: string
      email: string
      user_type: string
      status: string
      is_verified: boolean
    }
    institute: {
      id: number
      name: string
      email: string
      status: string
      is_verified: boolean
    }
    permissions: string[]
  }
}

class AuthService {
  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await api.post('/api/v1/auth/institute/register', data)
    return response.data
  }

  async login(data: LoginData): Promise<AuthResponse> {
    const response = await api.post('/api/v1/auth/institute/login', data)
    return response.data
  }

  async getCurrentUser(): Promise<UserResponse> {
    const response = await api.get('/api/v1/auth/institute/me')
    return response.data
  }

  async logout(): Promise<void> {
    await api.post('/api/v1/auth/institute/logout')
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await api.post('/api/v1/auth/institute/refresh', {
      refreshToken
    })
    return response.data
  }

  // Token management
  setTokens(tokens: { access_token: string; refresh_token: string }) {
    localStorage.setItem('access_token', tokens.access_token)
    localStorage.setItem('refresh_token', tokens.refresh_token)
  }

  getAccessToken(): string | null {
    return localStorage.getItem('access_token')
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token')
  }

  clearTokens() {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user')
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken()
  }
}

export const authService = new AuthService()
export default authService
