import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function runMigrations() {
  let connection

  try {
    console.log('🔄 Running database migrations...')

    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Check if database exists
    const [databases] = await connection.execute(
      'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
      [dbConfig.database]
    )

    if (databases.length === 0) {
      console.log('❌ Database does not exist. Please run "npm run db:setup" first.')
      process.exit(1)
    }

    // Check if tables exist
    const [tables] = await connection.execute('SHOW TABLES')

    if (tables.length === 0) {
      console.log('📋 No tables found. Running initial setup...')

      // Import and run setup
      const { execSync } = await import('child_process')
      execSync('node scripts/setup-database.js', { stdio: 'inherit' })

      console.log('✅ Initial migration completed')
    } else {
      console.log(`✅ Found ${tables.length} existing tables`)

      // Here you can add future migration logic
      // For now, we'll just verify all required tables exist
      const requiredTables = [
        'institutes', 'users', 'branches', 'courses', 'enrollments',
        'modules', 'roles', 'permissions', 'institute_module_access',
        'role_module_permissions', 'user_roles', 'user_sessions'
      ]
      const existingTableNames = tables.map(row => Object.values(row)[0])

      const missingTables = requiredTables.filter(table => !existingTableNames.includes(table))

      if (missingTables.length > 0) {
        console.log(`⚠️  Missing tables: ${missingTables.join(', ')}`)
        console.log('🔄 Running setup to create missing tables...')

        const { execSync } = await import('child_process')
        execSync('node scripts/setup-database.js', { stdio: 'inherit' })
      } else {
        console.log('✅ All required tables exist')
      }
    }

    console.log('🎉 Migrations completed successfully!')

  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

runMigrations()
