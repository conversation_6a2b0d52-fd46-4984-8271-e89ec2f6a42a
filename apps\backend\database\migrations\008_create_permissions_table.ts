import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'permissions'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.string('name', 100).notNullable().unique()
      table.string('display_name', 100).notNullable()
      table.string('description', 255).nullable()
      table.integer('module_id').unsigned().notNullable()
      
      // Timestamps
      table.timestamp('created_at', { useTz: true }).defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).defaultTo(this.now())
      
      // Foreign key to modules
      table.foreign('module_id').references('id').inTable('modules').onDelete('CASCADE')
      
      // Indexes
      table.index(['module_id'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}