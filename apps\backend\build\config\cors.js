import { defineConfig } from '@adonisjs/cors';
import env from '#start/env';
const corsConfig = defineConfig({
    enabled: env.get('CORS_ENABLED', true),
    origin: (origin, request) => {
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'https://lms-platform.com',
            env.get('FRONTEND_URL', 'http://localhost:3000')
        ];
        if (!origin)
            return true;
        return allowedOrigins.includes(origin);
    },
    methods: ['GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    headers: true,
    exposeHeaders: [
        'cache-control',
        'content-language',
        'content-type',
        'expires',
        'last-modified',
        'pragma',
    ],
    credentials: true,
    maxAge: 90,
});
export default corsConfig;
//# sourceMappingURL=cors.js.map