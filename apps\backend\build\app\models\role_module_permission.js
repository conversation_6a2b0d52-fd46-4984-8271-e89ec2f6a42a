var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm';
import Role from './role.js';
import Module from './module.js';
import Permission from './permission.js';
import Institute from './institute.js';
export default class RoleModulePermission extends BaseModel {
    get permissionKey() {
        return `${this.module?.name || 'unknown'}.${this.permission?.name || 'unknown'}`;
    }
    serialize() {
        return {
            id: this.id,
            roleId: this.roleId,
            moduleId: this.moduleId,
            permissionId: this.permissionId,
            instituteId: this.instituteId,
            permissionKey: this.permissionKey,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], RoleModulePermission.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], RoleModulePermission.prototype, "roleId", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], RoleModulePermission.prototype, "moduleId", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], RoleModulePermission.prototype, "permissionId", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], RoleModulePermission.prototype, "instituteId", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], RoleModulePermission.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], RoleModulePermission.prototype, "updatedAt", void 0);
__decorate([
    belongsTo(() => Role),
    __metadata("design:type", Object)
], RoleModulePermission.prototype, "role", void 0);
__decorate([
    belongsTo(() => Module),
    __metadata("design:type", Object)
], RoleModulePermission.prototype, "module", void 0);
__decorate([
    belongsTo(() => Permission),
    __metadata("design:type", Object)
], RoleModulePermission.prototype, "permission", void 0);
__decorate([
    belongsTo(() => Institute),
    __metadata("design:type", Object)
], RoleModulePermission.prototype, "institute", void 0);
//# sourceMappingURL=role_module_permission.js.map