"use client"

import { useToast } from "@/hooks/use-toast"
import { CheckCircle, AlertCircle, AlertTriangle, Info } from "lucide-react"

interface ApiResponse {
  success: boolean
  message: string
  data?: any
  errors?: any
}

interface ToastOptions {
  title?: string
  description?: string
  duration?: number
}

export function useApiToast() {
  const { toast } = useToast()

  const showSuccess = (message: string, options?: ToastOptions) => {
    toast({
      variant: "success",
      title: options?.title || "Success",
      description: message,
      duration: options?.duration || 5000,
    })
  }

  const showError = (message: string, options?: ToastOptions) => {
    toast({
      variant: "destructive",
      title: options?.title || "Error",
      description: message,
      duration: options?.duration || 7000,
    })
  }

  const showWarning = (message: string, options?: ToastOptions) => {
    toast({
      variant: "warning",
      title: options?.title || "Warning",
      description: message,
      duration: options?.duration || 6000,
    })
  }

  const showInfo = (message: string, options?: ToastOptions) => {
    toast({
      variant: "info",
      title: options?.title || "Information",
      description: message,
      duration: options?.duration || 5000,
    })
  }

  const handleApiResponse = (response: ApiResponse, successMessage?: string) => {
    if (response.success) {
      showSuccess(successMessage || response.message)
    } else {
      showError(response.message)
    }
  }

  const handleApiError = (error: any, defaultMessage = "An unexpected error occurred") => {
    const message = error?.response?.data?.message || error?.message || defaultMessage
    showError(message)
  }

  const showLoginSuccess = (userName?: string) => {
    showSuccess(
      `Welcome back${userName ? `, ${userName}` : ''}! Redirecting to dashboard...`,
      { title: "Login Successful" }
    )
  }

  const showRegistrationSuccess = () => {
    showSuccess(
      "Your institute has been registered successfully! Please wait for approval.",
      { title: "Registration Successful", duration: 8000 }
    )
  }

  const showLogoutSuccess = () => {
    showInfo(
      "You have been logged out successfully.",
      { title: "Logged Out" }
    )
  }

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    handleApiResponse,
    handleApiError,
    showLoginSuccess,
    showRegistrationSuccess,
    showLogoutSuccess,
  }
}
