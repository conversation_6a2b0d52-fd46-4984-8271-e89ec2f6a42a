import { DateTime } from 'luxon';
import UserSession from '#models/user_session';
export default class SessionService {
    async createSession(user, ctx, tokenId, tokenType, expiresAt) {
        const deviceInfo = this.parseDeviceInfo(ctx);
        const browserInfo = this.parseBrowserInfo(ctx);
        const locationInfo = await this.getLocationInfo(ctx.request.ip());
        await UserSession.query()
            .where('userId', user.id)
            .update({ isCurrentSession: false });
        const sessionData = {
            userId: user.id,
            sessionId: this.generateSessionId(),
            tokenId,
            tokenType,
            deviceType: deviceInfo.type,
            deviceName: deviceInfo.name,
            deviceModel: deviceInfo.model,
            deviceVendor: deviceInfo.vendor,
            operatingSystem: deviceInfo.operatingSystem,
            osVersion: deviceInfo.osVersion,
            isMobile: deviceInfo.isMobile,
            browserName: browserInfo.name,
            browserVersion: browserInfo.version,
            browserEngine: browserInfo.engine,
            userAgent: browserInfo.userAgent,
            ipAddress: ctx.request.ip(),
            country: locationInfo.country,
            region: locationInfo.region,
            city: locationInfo.city,
            timezone: locationInfo.timezone,
            isp: locationInfo.isp,
            loginAt: DateTime.now(),
            lastActivityAt: DateTime.now(),
            expiresAt,
            status: 'active',
            isCurrentSession: true,
            isTrustedDevice: await this.isTrustedDevice(user.id, ctx.request.ip(), deviceInfo),
            isBot: this.detectBot(browserInfo.userAgent),
            referrer: ctx.request.header('referer') || null,
            landingPage: ctx.request.url() || null,
            metadata: {
                loginMethod: 'password',
                clientVersion: ctx.request.header('x-client-version') || null,
            }
        };
        return await UserSession.create(sessionData);
    }
    async updateSessionActivity(sessionId) {
        await UserSession.query()
            .where('sessionId', sessionId)
            .where('status', 'active')
            .update({
            lastActivityAt: DateTime.now(),
            updatedAt: DateTime.now(),
        });
    }
    async revokeSession(sessionId, reason) {
        const updateData = {
            status: 'revoked',
            logoutAt: DateTime.now(),
            updatedAt: DateTime.now(),
        };
        if (reason) {
            updateData.securityNotes = reason;
        }
        await UserSession.query()
            .where('sessionId', sessionId)
            .update(updateData);
    }
    async revokeAllUserSessions(userId, exceptSessionId) {
        const query = UserSession.query()
            .where('userId', userId)
            .where('status', 'active');
        if (exceptSessionId) {
            query.whereNot('sessionId', exceptSessionId);
        }
        const result = await query.update({
            status: 'revoked',
            logoutAt: DateTime.now(),
            updatedAt: DateTime.now(),
        });
        return result[0] || 0;
    }
    async getUserActiveSessions(userId) {
        return await UserSession.query()
            .where('userId', userId)
            .where('status', 'active')
            .where('expiresAt', '>', DateTime.now().toSQL())
            .orderBy('lastActivityAt', 'desc');
    }
    async cleanupExpiredSessions() {
        const result = await UserSession.query()
            .where('expiresAt', '<=', DateTime.now().toSQL())
            .where('status', 'active')
            .update({
            status: 'expired',
            updatedAt: DateTime.now(),
        });
        return result[0] || 0;
    }
    async detectSuspiciousActivity(userId, ctx) {
        const recentSessions = await UserSession.query()
            .where('userId', userId)
            .where('loginAt', '>', DateTime.now().minus({ hours: 24 }).toSQL())
            .orderBy('loginAt', 'desc')
            .limit(10);
        const currentIp = ctx.request.ip();
        const deviceInfo = this.parseDeviceInfo(ctx);
        const uniqueIps = new Set(recentSessions.map(s => s.ipAddress));
        if (uniqueIps.size > 5) {
            return { isSuspicious: true, reason: 'Multiple IP addresses in 24 hours' };
        }
        const recentCountries = new Set(recentSessions.map(s => s.country).filter(Boolean));
        if (recentCountries.size > 0) {
            const locationInfo = await this.getLocationInfo(currentIp);
            if (locationInfo.country && !recentCountries.has(locationInfo.country)) {
                return { isSuspicious: true, reason: 'Login from new country' };
            }
        }
        const recentDevices = recentSessions.map(s => `${s.deviceType}-${s.operatingSystem}`);
        const currentDevice = `${deviceInfo.type}-${deviceInfo.operatingSystem}`;
        if (!recentDevices.includes(currentDevice)) {
            return { isSuspicious: true, reason: 'Login from new device type' };
        }
        return { isSuspicious: false };
    }
    parseDeviceInfo(ctx) {
        const userAgent = ctx.request.header('user-agent') || '';
        const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
        const isTablet = /iPad|Tablet/.test(userAgent);
        const isDesktop = !isMobile && !isTablet;
        let deviceType = 'unknown';
        if (isMobile)
            deviceType = 'mobile';
        else if (isTablet)
            deviceType = 'tablet';
        else if (isDesktop)
            deviceType = 'desktop';
        let operatingSystem = null;
        let osVersion = null;
        if (/Windows NT (\d+\.\d+)/.test(userAgent)) {
            operatingSystem = 'Windows';
            osVersion = userAgent.match(/Windows NT (\d+\.\d+)/)?.[1] || null;
        }
        else if (/Mac OS X (\d+[._]\d+[._]\d+)/.test(userAgent)) {
            operatingSystem = 'macOS';
            osVersion = userAgent.match(/Mac OS X (\d+[._]\d+[._]\d+)/)?.[1]?.replace(/_/g, '.') || null;
        }
        else if (/Android (\d+\.\d+)/.test(userAgent)) {
            operatingSystem = 'Android';
            osVersion = userAgent.match(/Android (\d+\.\d+)/)?.[1] || null;
        }
        else if (/iPhone OS (\d+[._]\d+[._]\d+)/.test(userAgent)) {
            operatingSystem = 'iOS';
            osVersion = userAgent.match(/iPhone OS (\d+[._]\d+[._]\d+)/)?.[1]?.replace(/_/g, '.') || null;
        }
        return {
            type: deviceType,
            name: null,
            model: null,
            vendor: null,
            operatingSystem,
            osVersion,
            isMobile,
        };
    }
    parseBrowserInfo(ctx) {
        const userAgent = ctx.request.header('user-agent') || '';
        let browserName = null;
        let browserVersion = null;
        let browserEngine = null;
        if (/Chrome\/(\d+\.\d+)/.test(userAgent)) {
            browserName = 'Chrome';
            browserVersion = userAgent.match(/Chrome\/(\d+\.\d+)/)?.[1] || null;
            browserEngine = 'Blink';
        }
        else if (/Firefox\/(\d+\.\d+)/.test(userAgent)) {
            browserName = 'Firefox';
            browserVersion = userAgent.match(/Firefox\/(\d+\.\d+)/)?.[1] || null;
            browserEngine = 'Gecko';
        }
        else if (/Safari\/(\d+\.\d+)/.test(userAgent) && !/Chrome/.test(userAgent)) {
            browserName = 'Safari';
            browserVersion = userAgent.match(/Version\/(\d+\.\d+)/)?.[1] || null;
            browserEngine = 'WebKit';
        }
        else if (/Edge\/(\d+\.\d+)/.test(userAgent)) {
            browserName = 'Edge';
            browserVersion = userAgent.match(/Edge\/(\d+\.\d+)/)?.[1] || null;
            browserEngine = 'EdgeHTML';
        }
        return {
            name: browserName,
            version: browserVersion,
            engine: browserEngine,
            userAgent,
        };
    }
    async getLocationInfo(ipAddress) {
        return {
            country: null,
            region: null,
            city: null,
            timezone: null,
            isp: null,
        };
    }
    async isTrustedDevice(userId, ipAddress, deviceInfo) {
        const recentTrustedSessions = await UserSession.query()
            .where('userId', userId)
            .where('isTrustedDevice', true)
            .where('loginAt', '>', DateTime.now().minus({ days: 30 }).toSQL());
        return recentTrustedSessions.some(session => session.ipAddress === ipAddress &&
            session.deviceType === deviceInfo.type &&
            session.operatingSystem === deviceInfo.operatingSystem);
    }
    detectBot(userAgent) {
        const botPatterns = [
            /bot/i,
            /crawler/i,
            /spider/i,
            /scraper/i,
            /curl/i,
            /wget/i,
            /python/i,
            /java/i,
        ];
        return botPatterns.some(pattern => pattern.test(userAgent));
    }
    generateSessionId() {
        return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
//# sourceMappingURL=session_service.js.map