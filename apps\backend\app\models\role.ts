import { DateTime } from 'luxon'
import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm'
import type { HasMany } from '@adonisjs/lucid/types/relations'
import RoleModulePermission from './role_module_permission.js'
import UserRole from './user_role.js'

export default class Role extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare displayName: string

  @column()
  declare description: string | null

  @column()
  declare isSystemRole: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationships
   */
  @hasMany(() => RoleModulePermission)
  declare modulePermissions: HasMany<typeof RoleModulePermission>

  @hasMany(() => UserRole)
  declare userRoles: HasMany<typeof UserRole>

  /**
   * Computed properties
   */
  get isSystem() {
    return this.isSystemRole
  }

  get isCustom() {
    return !this.isSystemRole
  }

  /**
   * Serialize role data for API responses
   */
  serialize() {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      description: this.description,
      isSystemRole: this.isSystemRole,
      isSystem: this.isSystem,
      isCustom: this.isCustom,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
