import { DateTime } from 'luxon';
import jwt from 'jsonwebtoken';
import env from '#start/env';
import User from '#models/user';
import Course from '#models/course';
import Enrollment from '#models/enrollment';
export default class InstituteAuthService {
    async generateTokens(user) {
        const payload = {
            userId: user.id,
            email: user.email,
            role: user.role,
            instituteId: user.instituteId,
            branchId: user.branchId,
            type: 'access'
        };
        const accessToken = jwt.sign(payload, env.get('JWT_SECRET'), {
            expiresIn: env.get('JWT_EXPIRES_IN', '24h'),
            issuer: 'lms-platform',
            audience: 'lms-users'
        });
        const refreshPayload = {
            userId: user.id,
            type: 'refresh'
        };
        const refreshToken = jwt.sign(refreshPayload, env.get('JWT_SECRET'), {
            expiresIn: env.get('JWT_REFRESH_EXPIRES_IN', '7d'),
            issuer: 'lms-platform',
            audience: 'lms-users'
        });
        return {
            accessToken,
            refreshToken,
            tokenType: 'Bearer',
            expiresIn: env.get('JWT_EXPIRES_IN', '24h')
        };
    }
    async refreshTokens(refreshToken) {
        try {
            const decoded = jwt.verify(refreshToken, env.get('JWT_SECRET'));
            if (decoded.type !== 'refresh') {
                throw new Error('Invalid token type');
            }
            const user = await User.find(decoded.userId);
            if (!user || !['institute_admin', 'institute_staff'].includes(user.role)) {
                throw new Error('User not found or invalid role');
            }
            return this.generateTokens(user);
        }
        catch (error) {
            throw new Error('Invalid refresh token');
        }
    }
    async updateLastLogin(user, ipAddress) {
        user.lastLoginAt = DateTime.now();
        user.lastLoginIp = ipAddress;
        await user.save();
    }
    async getUserPermissions(user) {
        const basePermissions = ['profile.read', 'profile.update'];
        if (user.role === 'institute_admin') {
            return [
                ...basePermissions,
                'students.create',
                'students.read',
                'students.update',
                'students.delete',
                'staff.create',
                'staff.read',
                'staff.update',
                'staff.delete',
                'courses.create',
                'courses.read',
                'courses.update',
                'courses.delete',
                'enrollments.create',
                'enrollments.read',
                'enrollments.update',
                'enrollments.delete',
                'institute.settings',
                'institute.analytics',
                'institute.billing'
            ];
        }
        if (user.role === 'institute_staff') {
            return [
                ...basePermissions,
                'students.read',
                'students.update',
                'courses.read',
                'courses.update',
                'enrollments.read',
                'enrollments.update'
            ];
        }
        return basePermissions;
    }
    async getDashboardData(user) {
        if (!user.instituteId) {
            throw new Error('User not associated with any institute');
        }
        const [totalStudents, activeStudents, totalStaff, totalCourses, activeCourses, recentEnrollments, courseStats] = await Promise.all([
            User.query()
                .where('institute_id', user.instituteId)
                .where('role', 'student')
                .count('* as total'),
            User.query()
                .where('institute_id', user.instituteId)
                .where('role', 'student')
                .where('status', 'active')
                .count('* as total'),
            User.query()
                .where('institute_id', user.instituteId)
                .whereIn('role', ['institute_admin', 'institute_staff'])
                .count('* as total'),
            Course.query()
                .where('institute_id', user.instituteId)
                .count('* as total'),
            Course.query()
                .where('institute_id', user.instituteId)
                .where('status', 'published')
                .count('* as total'),
            this.getRecentEnrollments(user.instituteId),
            this.getCourseStats(user.instituteId)
        ]);
        return {
            stats: {
                totalStudents: totalStudents[0].$extras.total,
                activeStudents: activeStudents[0].$extras.total,
                totalStaff: totalStaff[0].$extras.total,
                totalCourses: totalCourses[0].$extras.total,
                activeCourses: activeCourses[0].$extras.total
            },
            recentEnrollments,
            courseStats
        };
    }
    async getRecentEnrollments(instituteId) {
        const enrollments = await Enrollment.query()
            .preload('user', (query) => {
            query.select('id', 'first_name', 'last_name', 'student_id');
        })
            .preload('course', (query) => {
            query.select('id', 'title', 'thumbnail');
        })
            .whereHas('user', (query) => {
            query.where('institute_id', instituteId);
        })
            .orderBy('created_at', 'desc')
            .limit(10);
        return enrollments.map(enrollment => ({
            id: enrollment.id,
            student: {
                id: enrollment.user.id,
                name: enrollment.user.fullName,
                studentId: enrollment.user.studentId
            },
            course: {
                id: enrollment.course.id,
                title: enrollment.course.title,
                thumbnail: enrollment.course.thumbnail
            },
            status: enrollment.status,
            progress: enrollment.progress,
            enrolledAt: enrollment.enrolledAt
        }));
    }
    async getCourseStats(instituteId) {
        const courses = await Course.query()
            .where('institute_id', instituteId)
            .withCount('enrollments')
            .orderBy('enrollments_count', 'desc')
            .limit(5);
        return courses.map(course => ({
            id: course.id,
            title: course.title,
            thumbnail: course.thumbnail,
            enrollmentCount: course.$extras.enrollments_count,
            status: course.status,
            price: course.price
        }));
    }
    async logout(user) {
        user.updatedAt = DateTime.now();
        await user.save();
    }
}
//# sourceMappingURL=institute_auth_service.js.map