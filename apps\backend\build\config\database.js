import env from '#start/env';
import { defineConfig } from '@adonisjs/lucid';
const dbConfig = defineConfig({
    connection: env.get('DB_CONNECTION'),
    connections: {
        mysql: {
            client: 'mysql2',
            connection: {
                host: env.get('DB_HOST'),
                port: env.get('DB_PORT'),
                user: env.get('DB_USER'),
                password: env.get('DB_PASSWORD', ''),
                database: env.get('DB_DATABASE'),
                charset: 'utf8mb4',
                collation: 'utf8mb4_unicode_ci',
            },
            migrations: {
                naturalSort: true,
                paths: ['database/migrations'],
            },
            seeders: {
                paths: ['database/seeders'],
            },
            pool: {
                min: 2,
                max: 10,
            },
            acquireConnectionTimeout: 60000,
            useNullAsDefault: true,
            healthCheck: false,
            debug: false,
        },
    },
});
export default dbConfig;
//# sourceMappingURL=database.js.map