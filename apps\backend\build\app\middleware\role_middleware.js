export default class RoleMiddleware {
    async handle({ auth, response }, next, options) {
        await auth.authenticate();
        const user = auth.user;
        const allowedRoles = options.roles || [];
        if (!allowedRoles.includes(user.role)) {
            return response.status(403).json({
                success: false,
                message: 'Access denied',
                error: 'Insufficient permissions for this resource'
            });
        }
        return next();
    }
}
//# sourceMappingURL=role_middleware.js.map