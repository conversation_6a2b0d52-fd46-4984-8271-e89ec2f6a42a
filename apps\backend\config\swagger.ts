import swaggerJSDoc from 'swagger-jsdoc'

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'LMS Platform API',
      version: '1.0.0',
      description: `
# LMS Platform API Documentation

A comprehensive Learning Management System API supporting multiple user roles:

## 🎯 User Roles
- **Super Admin**: Platform-wide management and analytics
- **Institute Admin**: Institute-specific management and operations
- **Students**: Learning, assignments, and academic features

## 🔐 Authentication
All protected endpoints require JWT authentication. Use the login endpoints to obtain tokens.

## 📚 Getting Started
1. Choose your user role from the tags below
2. Use the appropriate login endpoint to authenticate
3. Include the JWT token in the Authorization header for protected endpoints

## 🏷️ API Organization
Endpoints are organized by user roles and functionality using tags for easy navigation.
      `,
      contact: {
        name: 'LMS Platform Team',
        email: '<EMAIL>',
        url: 'https://lmsplatform.com'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3333/api/v1',
        description: 'Development server'
      },
      {
        url: 'https://api.lmsplatform.com/api/v1',
        description: 'Production server'
      }
    ],
    tags: [
      {
        name: 'Super Admin Authentication',
        description: 'Authentication endpoints for super admin and LMS admin users'
      },
      {
        name: 'Super Admin - Institute Management',
        description: 'Institute management operations (create, update, suspend, activate)'
      },
      {
        name: 'Super Admin - User Management',
        description: 'User management across all institutes'
      },
      {
        name: 'Super Admin - Analytics',
        description: 'Platform-wide analytics and reporting'
      },
      {
        name: 'Institute Admin Authentication',
        description: 'Authentication and registration endpoints for institute administrators'
      },
      {
        name: 'Institute Admin - Student Management',
        description: 'Student management within the institute'
      },
      {
        name: 'Institute Admin - Course Management',
        description: 'Course creation and management'
      },
      {
        name: 'Institute Admin - Analytics',
        description: 'Institute-specific analytics and reporting'
      },
      {
        name: 'Student Authentication',
        description: 'Authentication endpoints for students'
      },
      {
        name: 'Student - Courses',
        description: 'Course access, enrollment, and learning materials'
      },
      {
        name: 'Student - Assignments',
        description: 'Assignment viewing and submission'
      },
      {
        name: 'Student - Grades',
        description: 'Grade viewing and academic transcript'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: { type: 'integer' },
            first_name: { type: 'string' },
            last_name: { type: 'string' },
            email: { type: 'string', format: 'email' },
            user_type: {
              type: 'string',
              enum: ['super_admin', 'lms_admin', 'institute_admin', 'institute_staff', 'student']
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'suspended', 'pending_approval']
            },
            is_verified: { type: 'boolean' },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        },
        Institute: {
          type: 'object',
          properties: {
            id: { type: 'integer' },
            name: { type: 'string' },
            slug: { type: 'string' },
            email: { type: 'string', format: 'email' },
            phone: { type: 'string' },
            address: { type: 'string' },
            type: {
              type: 'string',
              enum: ['university', 'college', 'school', 'training_center', 'coaching_institute', 'online_academy', 'corporate_training']
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'suspended', 'pending_approval']
            },
            subscription_plan: {
              type: 'string',
              enum: ['free', 'basic', 'premium', 'enterprise']
            },
            is_verified: { type: 'boolean' },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        },
        AuthTokens: {
          type: 'object',
          properties: {
            access_token: { type: 'string' },
            refresh_token: { type: 'string' },
            expires_in: { type: 'integer' },
            token_type: { type: 'string', default: 'Bearer' }
          }
        },
        ApiResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: { type: 'object' },
            errors: { type: 'object' }
          }
        },
        ValidationError: {
          type: 'object',
          properties: {
            success: { type: 'boolean', default: false },
            message: { type: 'string' },
            errors: {
              type: 'object',
              additionalProperties: {
                type: 'array',
                items: { type: 'string' }
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './app/controllers/auth/*.ts',
    './app/controllers/super_admin/*.ts',
    './app/controllers/institute_admin/*.ts',
    './app/controllers/student/*.ts',
    './docs/swagger/*.yaml',
    './start/routes.ts'
  ]
}

export const swaggerSpec = swaggerJSDoc(options)
