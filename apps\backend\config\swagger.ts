import swaggerJSDoc from 'swagger-jsdoc'

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'LMS Platform API',
      version: '1.0.0',
      description: 'A comprehensive Learning Management System API',
      contact: {
        name: 'LMS Platform Team',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: 'http://localhost:3333/api/v1',
        description: 'Development server'
      },
      {
        url: 'https://api.lmsplatform.com/api/v1',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: { type: 'integer' },
            first_name: { type: 'string' },
            last_name: { type: 'string' },
            email: { type: 'string', format: 'email' },
            user_type: { 
              type: 'string', 
              enum: ['super_admin', 'lms_admin', 'institute_admin', 'institute_staff', 'student'] 
            },
            status: { 
              type: 'string', 
              enum: ['active', 'inactive', 'suspended', 'pending_approval'] 
            },
            is_verified: { type: 'boolean' },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        },
        Institute: {
          type: 'object',
          properties: {
            id: { type: 'integer' },
            name: { type: 'string' },
            slug: { type: 'string' },
            email: { type: 'string', format: 'email' },
            phone: { type: 'string' },
            address: { type: 'string' },
            type: { 
              type: 'string', 
              enum: ['university', 'college', 'school', 'training_center', 'coaching_institute', 'online_academy', 'corporate_training'] 
            },
            status: { 
              type: 'string', 
              enum: ['active', 'inactive', 'suspended', 'pending_approval'] 
            },
            subscription_plan: { 
              type: 'string', 
              enum: ['free', 'basic', 'premium', 'enterprise'] 
            },
            is_verified: { type: 'boolean' },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        },
        AuthTokens: {
          type: 'object',
          properties: {
            access_token: { type: 'string' },
            refresh_token: { type: 'string' },
            expires_in: { type: 'integer' },
            token_type: { type: 'string', default: 'Bearer' }
          }
        },
        ApiResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: { type: 'object' },
            errors: { type: 'object' }
          }
        },
        ValidationError: {
          type: 'object',
          properties: {
            success: { type: 'boolean', default: false },
            message: { type: 'string' },
            errors: {
              type: 'object',
              additionalProperties: {
                type: 'array',
                items: { type: 'string' }
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './app/controllers/auth/*.ts',
    './start/routes.ts'
  ]
}

export const swaggerSpec = swaggerJSDoc(options)
