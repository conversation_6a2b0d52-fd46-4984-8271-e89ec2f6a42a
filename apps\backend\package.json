{"name": "@lms/backend", "version": "1.0.0", "private": true, "type": "module", "license": "MIT", "description": "LMS Platform Backend - AdonisJS 6.x API", "scripts": {"start": "node bin/server.js", "build": "node ace build", "dev": "node server-simple.js", "dev:watch": "node ace serve --watch", "test": "npx tsx ace test", "lint": "eslint . --ext=.ts", "format": "prettier --write .", "typecheck": "tsc --noEmit", "db:migrate": "npx tsx ace migration:run", "db:rollback": "npx tsx ace migration:rollback", "db:seed": "npx tsx ace db:seed", "db:fresh": "npx tsx ace migration:fresh --seed", "make:model": "npx tsx ace make:model", "make:controller": "npx tsx ace make:controller", "make:middleware": "npx tsx ace make:middleware", "make:migration": "npx tsx ace make:migration", "make:seeder": "npx tsx ace make:seeder"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^1.2.1", "@adonisjs/prettier-config": "^1.2.1", "@adonisjs/tsconfig": "^1.2.1", "@japa/api-client": "^2.0.2", "@japa/assert": "^2.1.0", "@japa/plugin-adonisjs": "^3.0.0", "@japa/runner": "^3.1.1", "@swc/core": "^1.3.107", "@types/luxon": "^3.4.2", "@types/node": "^20.11.5", "@types/uuid": "^9.0.7", "eslint": "^8.56.0", "hot-hook": "^0.2.6", "nodemon": "^3.1.10", "pino-pretty": "^10.3.1", "prettier": "^3.2.4", "ts-node": "^10.9.2", "tsx": "^4.19.4", "turbo": "^2.5.4", "typescript": "^5.3.3"}, "dependencies": {"@adonisjs/auth": "^9.1.1", "@adonisjs/bouncer": "^3.1.0", "@adonisjs/core": "^6.18.0", "@adonisjs/cors": "^2.2.1", "@adonisjs/drive": "^3.1.0", "@adonisjs/limiter": "^2.1.0", "@adonisjs/lucid": "^20.1.0", "@adonisjs/mail": "^9.1.0", "@adonisjs/redis": "^9.2.0", "@adonisjs/session": "^7.1.1", "@adonisjs/shield": "^8.1.1", "@adonisjs/static": "^1.1.1", "@vinejs/vine": "^1.7.1", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "edge.js": "^6.0.2", "express": "^5.1.0", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "luxon": "^3.4.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "nodemailer": "^6.9.8", "objection": "^3.1.5", "reflect-metadata": "^0.2.1", "sharp": "^0.33.1", "stripe": "^14.12.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "eslintConfig": {"extends": "@adonisjs/eslint-config/app"}, "prettier": "@adonisjs/prettier-config"}