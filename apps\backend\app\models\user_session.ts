import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'

export default class UserSession extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare sessionId: string

  @column()
  declare tokenId: string

  @column()
  declare tokenType: 'access' | 'refresh'

  // Device and Browser Information
  @column()
  declare deviceType: string | null

  @column()
  declare deviceName: string | null

  @column()
  declare deviceModel: string | null

  @column()
  declare deviceVendor: string | null

  @column()
  declare operatingSystem: string | null

  @column()
  declare osVersion: string | null

  @column()
  declare browserName: string | null

  @column()
  declare browserVersion: string | null

  @column()
  declare browserEngine: string | null

  @column()
  declare userAgent: string | null

  // Network Information
  @column()
  declare ipAddress: string

  @column()
  declare country: string | null

  @column()
  declare region: string | null

  @column()
  declare city: string | null

  @column()
  declare timezone: string | null

  @column()
  declare isp: string | null

  // Session Details
  @column.dateTime()
  declare loginAt: DateTime

  @column.dateTime()
  declare lastActivityAt: DateTime

  @column.dateTime()
  declare expiresAt: DateTime

  @column.dateTime()
  declare logoutAt: DateTime | null

  // Session Status and Security
  @column()
  declare status: 'active' | 'expired' | 'revoked' | 'logged_out'

  @column()
  declare isCurrentSession: boolean

  @column()
  declare isTrustedDevice: boolean

  @column()
  declare isMobile: boolean

  @column()
  declare isBot: boolean

  @column()
  declare isSuspicious: boolean

  @column()
  declare securityNotes: string | null

  // Additional metadata
  @column()
  declare metadata: Record<string, any> | null

  @column()
  declare referrer: string | null

  @column()
  declare landingPage: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationships
   */
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  /**
   * Computed properties
   */
  get isActive() {
    return this.status === 'active' && this.expiresAt > DateTime.now()
  }

  get isExpired() {
    return this.expiresAt <= DateTime.now()
  }

  get isLoggedOut() {
    return this.status === 'logged_out' || this.logoutAt !== null
  }

  get isRevoked() {
    return this.status === 'revoked'
  }

  get sessionDuration() {
    const endTime = this.logoutAt || this.lastActivityAt
    return endTime.diff(this.loginAt).as('minutes')
  }

  get deviceInfo() {
    return {
      type: this.deviceType,
      name: this.deviceName,
      model: this.deviceModel,
      vendor: this.deviceVendor,
      os: this.operatingSystem,
      osVersion: this.osVersion,
      isMobile: this.isMobile,
    }
  }

  get browserInfo() {
    return {
      name: this.browserName,
      version: this.browserVersion,
      engine: this.browserEngine,
      userAgent: this.userAgent,
    }
  }

  get locationInfo() {
    return {
      ip: this.ipAddress,
      country: this.country,
      region: this.region,
      city: this.city,
      timezone: this.timezone,
      isp: this.isp,
    }
  }

  get securityInfo() {
    return {
      isTrusted: this.isTrustedDevice,
      isSuspicious: this.isSuspicious,
      isBot: this.isBot,
      notes: this.securityNotes,
    }
  }

  /**
   * Static methods for session management
   */
  static async createSession(data: Partial<UserSession>) {
    return await UserSession.create(data)
  }

  static async findActiveSession(sessionId: string) {
    return await UserSession.query()
      .where('sessionId', sessionId)
      .where('status', 'active')
      .where('expiresAt', '>', DateTime.now().toSQL())
      .first()
  }

  static async findUserActiveSessions(userId: number) {
    return await UserSession.query()
      .where('userId', userId)
      .where('status', 'active')
      .where('expiresAt', '>', DateTime.now().toSQL())
      .orderBy('lastActivityAt', 'desc')
  }

  static async revokeSession(sessionId: string) {
    return await UserSession.query()
      .where('sessionId', sessionId)
      .update({
        status: 'revoked',
        logoutAt: DateTime.now(),
        updatedAt: DateTime.now(),
      })
  }

  static async revokeAllUserSessions(userId: number, exceptSessionId?: string) {
    const query = UserSession.query()
      .where('userId', userId)
      .where('status', 'active')

    if (exceptSessionId) {
      query.whereNot('sessionId', exceptSessionId)
    }

    return await query.update({
      status: 'revoked',
      logoutAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })
  }

  static async cleanupExpiredSessions() {
    return await UserSession.query()
      .where('expiresAt', '<=', DateTime.now().toSQL())
      .where('status', 'active')
      .update({
        status: 'expired',
        updatedAt: DateTime.now(),
      })
  }

  /**
   * Instance methods
   */
  async updateActivity() {
    this.lastActivityAt = DateTime.now()
    await this.save()
  }

  async markAsLoggedOut() {
    this.status = 'logged_out'
    this.logoutAt = DateTime.now()
    await this.save()
  }

  async markAsRevoked(reason?: string) {
    this.status = 'revoked'
    this.logoutAt = DateTime.now()
    if (reason) {
      this.securityNotes = reason
    }
    await this.save()
  }

  async markAsSuspicious(reason: string) {
    this.isSuspicious = true
    this.securityNotes = reason
    await this.save()
  }

  async markAsTrusted() {
    this.isTrustedDevice = true
    await this.save()
  }

  /**
   * Serialize session data for API responses
   */
  serialize() {
    return {
      id: this.id,
      sessionId: this.sessionId,
      tokenType: this.tokenType,
      device: this.deviceInfo,
      browser: this.browserInfo,
      location: this.locationInfo,
      security: this.securityInfo,
      loginAt: this.loginAt,
      lastActivityAt: this.lastActivityAt,
      expiresAt: this.expiresAt,
      logoutAt: this.logoutAt,
      status: this.status,
      isCurrentSession: this.isCurrentSession,
      sessionDuration: this.sessionDuration,
      referrer: this.referrer,
      landingPage: this.landingPage,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }

  /**
   * Serialize minimal session data for security-sensitive contexts
   */
  serializeMinimal() {
    return {
      id: this.id,
      deviceType: this.deviceType,
      browserName: this.browserName,
      location: `${this.city || 'Unknown'}, ${this.country || 'Unknown'}`,
      loginAt: this.loginAt,
      lastActivityAt: this.lastActivityAt,
      isCurrentSession: this.isCurrentSession,
      status: this.status,
    }
  }
}
