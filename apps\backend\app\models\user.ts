import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import Institute from './institute.js'
import Branch from './branch.js'
import Enrollment from './enrollment.js'
import UserRole from './user_role.js'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email', 'student_id'],
  passwordColumnName: 'password',
})

export default class User extends compose(BaseModel, AuthFinder) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare email: string | null

  @column()
  declare studentId: string | null

  @column({ serializeAs: null })
  declare password: string

  @column()
  declare firstName: string

  @column()
  declare lastName: string

  @column()
  declare phone: string | null

  @column()
  declare avatar: string | null

  @column()
  declare role: 'super_admin' | 'lms_admin' | 'institute_admin' | 'institute_staff' | 'student'

  @column()
  declare status: 'active' | 'inactive' | 'suspended' | 'pending'

  @column()
  declare instituteId: number | null

  @column()
  declare branchId: number | null

  @column()
  declare emailVerifiedAt: DateTime | null

  @column()
  declare lastLoginAt: DateTime | null

  @column()
  declare lastLoginIp: string | null

  @column()
  declare twoFactorEnabled: boolean

  @column()
  declare twoFactorSecret: string | null

  @column()
  declare preferences: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  /**
   * Relationships
   */
  @belongsTo(() => Institute)
  declare institute: BelongsTo<typeof Institute>

  @belongsTo(() => Branch)
  declare branch: BelongsTo<typeof Branch>

  @hasMany(() => Enrollment)
  declare enrollments: HasMany<typeof Enrollment>

  @hasMany(() => UserRole)
  declare userRoles: HasMany<typeof UserRole>

  /**
   * Computed properties
   */
  get fullName() {
    return `${this.firstName} ${this.lastName}`
  }

  get initials() {
    return `${this.firstName.charAt(0)}${this.lastName.charAt(0)}`.toUpperCase()
  }

  get isActive() {
    return this.status === 'active'
  }

  get isSuperAdmin() {
    return this.role === 'super_admin'
  }

  get isLmsAdmin() {
    return this.role === 'lms_admin'
  }

  get isInstituteAdmin() {
    return this.role === 'institute_admin'
  }

  get isInstituteStaff() {
    return this.role === 'institute_staff'
  }

  get isStudent() {
    return this.role === 'student'
  }

  /**
   * Hooks
   */
  static async hashPassword(user: User) {
    if (user.$dirty.password) {
      user.password = await hash.make(user.password)
    }
  }

  /**
   * Serialize user data for API responses
   */
  serialize() {
    return {
      id: this.id,
      email: this.email,
      studentId: this.studentId,
      firstName: this.firstName,
      lastName: this.lastName,
      fullName: this.fullName,
      initials: this.initials,
      phone: this.phone,
      avatar: this.avatar,
      role: this.role,
      status: this.status,
      instituteId: this.instituteId,
      branchId: this.branchId,
      emailVerifiedAt: this.emailVerifiedAt,
      lastLoginAt: this.lastLoginAt,
      twoFactorEnabled: this.twoFactorEnabled,
      preferences: this.preferences,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
