'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAppSelector } from '@/store'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: string[]
  redirectTo?: string
}

export default function ProtectedRoute({ 
  children, 
  requiredRole = [], 
  redirectTo = '/auth-login/login' 
}: ProtectedRouteProps) {
  const { isAuthenticated, user, isLoading } = useAppSelector((state) => state.auth)
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo)
      return
    }

    if (isAuthenticated && user && requiredRole.length > 0) {
      if (!requiredRole.includes(user.user_type)) {
        // User doesn't have required role, redirect to appropriate dashboard
        switch (user.user_type) {
          case 'super_admin':
          case 'lms_admin':
            router.push('/super-admin')
            break
          case 'institute_admin':
          case 'institute_staff':
            router.push('/institute-admin')
            break
          case 'student':
            router.push('/student')
            break
          default:
            router.push('/auth-login/login')
        }
        return
      }
    }
  }, [isAuthenticated, user, isLoading, requiredRole, router, redirectTo])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Will redirect in useEffect
  }

  if (requiredRole.length > 0 && user && !requiredRole.includes(user.user_type)) {
    return null // Will redirect in useEffect
  }

  return <>{children}</>
}
