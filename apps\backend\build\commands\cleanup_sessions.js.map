{"version": 3, "file": "cleanup_sessions.js", "sourceRoot": "", "sources": ["../../commands/cleanup_sessions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAA;AAEhD,OAAO,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAA;AAChC,OAAO,WAAW,MAAM,sBAAsB,CAAA;AAC9C,OAAO,cAAc,MAAM,2BAA2B,CAAA;AAEtD,MAAM,CAAC,OAAO,OAAO,eAAgB,SAAQ,WAAW;IACtD,MAAM,CAAC,WAAW,GAAG,kBAAkB,CAAA;IACvC,MAAM,CAAC,WAAW,GAAG,wCAAwC,CAAA;IAE7D,MAAM,CAAC,OAAO,GAAmB;QAC/B,QAAQ,EAAE,IAAI;QACd,iBAAiB,EAAE,KAAK;QACxB,UAAU,EAAE,KAAK;KAClB,CAAA;IAED,KAAK,CAAC,GAAG;QACP,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;QAE/C,MAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAA;QAE3C,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,sBAAsB,EAAE,CAAA;YAClE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,YAAY,mBAAmB,CAAC,CAAA;YAG3D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;YACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,gBAAgB,eAAe,CAAC,CAAA;YAG5D,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAA;YAClE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,oBAAoB,uBAAuB,CAAC,CAAA;YAGxE,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAA;YAElC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAA;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;YAC3D,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;QACnB,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAA;QAErD,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;aACrC,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC;aAC3C,MAAM,EAAE,CAAA;QAEX,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACvB,CAAC;IAKO,KAAK,CAAC,wBAAwB;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAA;QAErD,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;aACrC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;aAC1B,KAAK,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC;aAC1C,MAAM,EAAE,CAAA;QAEX,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACvB,CAAC;IAKO,KAAK,CAAC,qBAAqB;QACjC,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;aACpC,MAAM,CAAC,QAAQ,CAAC;aAChB,KAAK,CAAC,YAAY,CAAC;aACnB,OAAO,CAAC,QAAQ,CAAC,CAAA;QAEpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;QAE/C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QACrD,CAAC;QAGD,MAAM,aAAa,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QACnE,MAAM,cAAc,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;aAC7C,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;aACzB,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;aAC/C,KAAK,CAAC,YAAY,CAAC,CAAA;QAEtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;QAC7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;IACjE,CAAC"}