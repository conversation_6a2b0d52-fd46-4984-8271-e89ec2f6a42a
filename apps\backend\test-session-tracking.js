import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function testSessionTracking() {
  let connection

  try {
    console.log('🔍 Testing session tracking...')

    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Check current session count
    const [beforeSessions] = await connection.execute('SELECT COUNT(*) as count FROM user_sessions')
    console.log(`📊 Sessions before login: ${beforeSessions[0].count}`)

    // Test login API call
    console.log('\n🔐 Testing login API...')

    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    }

    const response = await fetch('http://localhost:3333/api/v1/auth/institute/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      },
      body: JSON.stringify(loginData)
    })

    const result = await response.json()

    if (result.success) {
      console.log('✅ Login successful')
      console.log(`👤 User: ${result.data.user.firstName} ${result.data.user.lastName}`)
      console.log(`🏢 Institute: ${result.data.institute.name}`)
      console.log(`🔑 Token: ${result.data.tokens.accessToken.substring(0, 50)}...`)
    } else {
      console.log('❌ Login failed:', result.message)
      return
    }

    // Wait a moment for session to be created
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Check session count after login
    const [afterSessions] = await connection.execute('SELECT COUNT(*) as count FROM user_sessions')
    console.log(`\n📊 Sessions after login: ${afterSessions[0].count}`)

    // Get the latest session details
    const [latestSession] = await connection.execute(`
      SELECT
        user_id, session_id, token_id, device_type, browser_name,
        operating_system, ip_address, status, is_mobile, login_at
      FROM user_sessions
      ORDER BY login_at DESC
      LIMIT 1
    `)

    if (latestSession.length > 0) {
      const session = latestSession[0]
      console.log('\n🎯 Latest session details:')
      console.log(`   User ID: ${session.user_id}`)
      console.log(`   Session ID: ${session.session_id}`)
      console.log(`   Token ID: ${session.token_id}`)
      console.log(`   Device Type: ${session.device_type}`)
      console.log(`   Browser: ${session.browser_name}`)
      console.log(`   OS: ${session.operating_system}`)
      console.log(`   IP Address: ${session.ip_address}`)
      console.log(`   Status: ${session.status}`)
      console.log(`   Is Mobile: ${session.is_mobile}`)
      console.log(`   Login Time: ${session.login_at}`)
    }

    // Test logout
    console.log('\n🚪 Testing logout API...')

    const logoutResponse = await fetch('http://localhost:3333/api/v1/auth/logout', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${result.data.tokens.accessToken}`,
        'Content-Type': 'application/json'
      }
    })

    const logoutResult = await logoutResponse.json()

    if (logoutResult.success) {
      console.log('✅ Logout successful')
    } else {
      console.log('❌ Logout failed:', logoutResult.message)
    }

    // Wait a moment for session to be updated
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Check session status after logout
    const [loggedOutSession] = await connection.execute(`
      SELECT status, logout_at, is_current_session
      FROM user_sessions
      ORDER BY login_at DESC
      LIMIT 1
    `)

    if (loggedOutSession.length > 0) {
      const session = loggedOutSession[0]
      console.log('\n📊 Session after logout:')
      console.log(`   Status: ${session.status}`)
      console.log(`   Logout Time: ${session.logout_at}`)
      console.log(`   Is Current: ${session.is_current_session}`)
    }

    console.log('\n🎉 Session tracking test completed!')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

testSessionTracking()
