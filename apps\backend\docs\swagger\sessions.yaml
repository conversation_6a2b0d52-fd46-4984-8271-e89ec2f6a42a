paths:
  # Session Management
  /sessions:
    get:
      tags:
        - Session Management
      summary: List User Sessions
      description: Get list of current user's active sessions
      security:
        - bearerAuth: []
      responses:
        200:
          description: Sessions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      sessions:
                        type: array
                        items:
                          $ref: '#/components/schemas/UserSessionMinimal'
                      total:
                        type: integer
                        example: 3
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /sessions/{id}:
    get:
      tags:
        - Session Management
      summary: Get Session Details
      description: Get detailed information about a specific session
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Session details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/UserSession'
        401:
          $ref: '#/components/responses/Unauthorized'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/InternalServerError'

  /sessions/{id}/revoke:
    delete:
      tags:
        - Session Management
      summary: Revoke Session
      description: Revoke a specific session
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Session revoked successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Session revoked successfully"
        401:
          $ref: '#/components/responses/Unauthorized'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/InternalServerError'

  /sessions/revoke-all:
    delete:
      tags:
        - Session Management
      summary: Revoke All Sessions
      description: Revoke all sessions except the current one
      security:
        - bearerAuth: []
      responses:
        200:
          description: Sessions revoked successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "3 sessions revoked successfully"
                  data:
                    type: object
                    properties:
                      revokedCount:
                        type: integer
                        example: 3
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /sessions/{id}/trust:
    post:
      tags:
        - Session Management
      summary: Trust Device
      description: Mark a device as trusted for future logins
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Device marked as trusted
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Device marked as trusted"
        401:
          $ref: '#/components/responses/Unauthorized'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/InternalServerError'

  /sessions/analytics:
    get:
      tags:
        - Session Management
      summary: Get Session Analytics
      description: Get analytics about user's session activity
      security:
        - bearerAuth: []
      responses:
        200:
          description: Session analytics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      totalSessions:
                        type: integer
                        example: 15
                      activeSessions:
                        type: integer
                        example: 2
                      deviceTypes:
                        type: object
                        properties:
                          desktop:
                            type: integer
                            example: 8
                          mobile:
                            type: integer
                            example: 7
                      browsers:
                        type: object
                        properties:
                          Chrome:
                            type: integer
                            example: 10
                          Safari:
                            type: integer
                            example: 5
                      operatingSystems:
                        type: object
                        properties:
                          macOS:
                            type: integer
                            example: 8
                          iOS:
                            type: integer
                            example: 5
                          Windows:
                            type: integer
                            example: 2
                      countries:
                        type: object
                        properties:
                          "United States":
                            type: integer
                            example: 12
                          "Canada":
                            type: integer
                            example: 3
                      suspiciousAttempts:
                        type: integer
                        example: 1
                      averageSessionDuration:
                        type: number
                        example: 45.5
                      loginTrends:
                        type: object
                        properties:
                          "2024-01-15":
                            type: integer
                            example: 2
                          "2024-01-16":
                            type: integer
                            example: 1
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /sessions/security-alerts:
    get:
      tags:
        - Session Management
      summary: Get Security Alerts
      description: Get security alerts related to suspicious login activity
      security:
        - bearerAuth: []
      responses:
        200:
          description: Security alerts retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      alerts:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 123
                            type:
                              type: string
                              example: "suspicious_login"
                            severity:
                              type: string
                              enum: [low, medium, high]
                              example: "medium"
                            message:
                              type: string
                              example: "Login from new country"
                            timestamp:
                              type: string
                              format: date-time
                            details:
                              type: object
                              properties:
                                location:
                                  type: string
                                  example: "Moscow, Russia"
                                device:
                                  type: string
                                  example: "desktop - Linux"
                                browser:
                                  type: string
                                  example: "Firefox"
                                ipAddress:
                                  type: string
                                  example: "************"
                      total:
                        type: integer
                        example: 1
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    UserSession:
      type: object
      properties:
        id:
          type: integer
          example: 1
        sessionId:
          type: string
          example: "sess_1642678800_abc123def"
        tokenType:
          type: string
          enum: [access, refresh]
          example: "access"
        device:
          type: object
          properties:
            type:
              type: string
              example: "desktop"
            name:
              type: string
              example: "MacBook Pro"
            model:
              type: string
              example: "MacBook Pro 16-inch"
            vendor:
              type: string
              example: "Apple"
            os:
              type: string
              example: "macOS"
            osVersion:
              type: string
              example: "14.0"
            isMobile:
              type: boolean
              example: false
        browser:
          type: object
          properties:
            name:
              type: string
              example: "Chrome"
            version:
              type: string
              example: "119.0"
            engine:
              type: string
              example: "Blink"
            userAgent:
              type: string
              example: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)..."
        location:
          type: object
          properties:
            ip:
              type: string
              example: "*************"
            country:
              type: string
              example: "United States"
            region:
              type: string
              example: "California"
            city:
              type: string
              example: "San Francisco"
            timezone:
              type: string
              example: "America/Los_Angeles"
            isp:
              type: string
              example: "Comcast Cable"
        security:
          type: object
          properties:
            isTrusted:
              type: boolean
              example: true
            isSuspicious:
              type: boolean
              example: false
            isBot:
              type: boolean
              example: false
            notes:
              type: string
              example: null
        loginAt:
          type: string
          format: date-time
        lastActivityAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time
        logoutAt:
          type: string
          format: date-time
        status:
          type: string
          enum: [active, expired, revoked, logged_out]
          example: "active"
        isCurrentSession:
          type: boolean
          example: true
        sessionDuration:
          type: number
          example: 120.5
        referrer:
          type: string
          example: "https://google.com"
        landingPage:
          type: string
          example: "/dashboard"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UserSessionMinimal:
      type: object
      properties:
        id:
          type: integer
          example: 1
        deviceType:
          type: string
          example: "desktop"
        browserName:
          type: string
          example: "Chrome"
        location:
          type: string
          example: "San Francisco, United States"
        loginAt:
          type: string
          format: date-time
        lastActivityAt:
          type: string
          format: date-time
        isCurrentSession:
          type: boolean
          example: true
        status:
          type: string
          enum: [active, expired, revoked, logged_out]
          example: "active"
