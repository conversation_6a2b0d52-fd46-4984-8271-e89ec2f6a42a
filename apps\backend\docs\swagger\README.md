# LMS Platform API Documentation

This directory contains comprehensive Swagger/OpenAPI documentation for the LMS Platform API, organized by user roles and functionality.

## 📁 Documentation Structure

```
docs/swagger/
├── README.md              # This file
├── schemas.yaml           # Common schemas and responses
├── super-admin.yaml       # Super Admin endpoints
├── institute-admin.yaml   # Institute Admin endpoints
└── student.yaml          # Student endpoints
```

## 🚀 Accessing the Documentation

### Development Environment
- **Swagger UI**: http://localhost:3333/docs
- **API Base URL**: http://localhost:3333/api/v1

### Production Environment
- **Swagger UI**: https://api.lmsplatform.com/docs
- **API Base URL**: https://api.lmsplatform.com/api/v1

## 👥 User Roles & Access

### 1. Super Admin
**Role**: `super_admin`, `lms_admin`
**Access**: Platform-wide management capabilities

**Key Features**:
- ✅ Institute management (create, update, suspend, activate)
- ✅ User management across all institutes
- ✅ Platform analytics and reporting
- ✅ System configuration

**Authentication Endpoint**: `/auth/super-admin/login`

**Demo Credentials**:
```json
{
  "email": "<EMAIL>",
  "password": "superadmin123"
}
```

### 2. Institute Admin
**Role**: `institute_admin`, `institute_staff`
**Access**: Institute-specific management capabilities

**Key Features**:
- ✅ Student management within institute
- ✅ Course management and creation
- ✅ Instructor management
- ✅ Institute analytics and reporting
- ✅ Enrollment management

**Authentication Endpoint**: `/auth/institute/login`

**Demo Credentials**:
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

### 3. Students
**Role**: `student`
**Access**: Learning and academic features

**Key Features**:
- ✅ Course enrollment and access
- ✅ Assignment submission
- ✅ Grade viewing and transcript
- ✅ Profile management
- ✅ Learning progress tracking

**Authentication Endpoint**: `/auth/student/login`

**Demo Credentials**:
```json
{
  "email": "<EMAIL>",
  "password": "student123"
}
```

## 🔐 Authentication

All protected endpoints require JWT authentication using the `Authorization` header:

```
Authorization: Bearer <your-jwt-token>
```

### Token Flow
1. **Login** → Get access token and refresh token
2. **Use access token** for API requests
3. **Refresh token** when access token expires
4. **Logout** to invalidate tokens

### Token Expiration
- **Access Token**: 1 hour
- **Refresh Token**: 7 days

## 📊 API Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message",
  "errors": {
    "field": ["Validation error message"]
  }
}
```

### Pagination Response
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "currentPage": 1,
      "perPage": 20,
      "total": 150,
      "lastPage": 8,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

## 🏷️ API Tags Organization

### Super Admin Tags
- **Super Admin Authentication** - Login, logout, profile
- **Super Admin - Institute Management** - Institute CRUD operations
- **Super Admin - User Management** - User management across institutes
- **Super Admin - Analytics** - Platform-wide analytics

### Institute Admin Tags
- **Institute Admin Authentication** - Registration, login, logout
- **Institute Admin - Student Management** - Student CRUD operations
- **Institute Admin - Course Management** - Course CRUD operations
- **Institute Admin - Analytics** - Institute-specific analytics

### Student Tags
- **Student Authentication** - Login, logout, profile
- **Student - Courses** - Course access and enrollment
- **Student - Assignments** - Assignment viewing and submission
- **Student - Grades** - Grade viewing and transcript

## 🔍 Common Query Parameters

### Pagination
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20) - Items per page

### Filtering
- `status` - Filter by status (varies by endpoint)
- `search` - Search by name, email, or title
- `course_id` - Filter by specific course
- `institute_id` - Filter by specific institute

### Sorting
- `sort` - Sort field (e.g., 'name', 'created_at')
- `order` - Sort order ('asc' or 'desc')

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 422 | Validation Error |
| 500 | Internal Server Error |

## 🧪 Testing the API

### Using Swagger UI
1. Navigate to the Swagger UI URL
2. Click "Authorize" button
3. Enter your JWT token
4. Test endpoints directly from the interface

### Using cURL
```bash
# Login
curl -X POST http://localhost:3333/api/v1/auth/super-admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"superadmin123"}'

# Use token for protected endpoints
curl -X GET http://localhost:3333/api/v1/super-admin/institutes \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Using Postman
1. Import the OpenAPI specification
2. Set up environment variables for base URL and tokens
3. Use the pre-configured requests

## 📝 Notes

- All timestamps are in ISO 8601 format with timezone
- File uploads use multipart/form-data
- Bulk operations are available for certain endpoints
- Rate limiting is applied to authentication endpoints
- API versioning is handled through URL prefix (`/api/v1`)

## 🔄 Updates

This documentation is automatically updated when the API changes. For the latest version, always refer to the live Swagger UI interface.
