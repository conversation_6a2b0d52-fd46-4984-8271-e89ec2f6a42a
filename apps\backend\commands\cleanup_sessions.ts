import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import { DateTime } from 'luxon'
import UserSession from '#models/user_session'
import SessionService from '#services/session_service'

export default class CleanupSessions extends BaseCommand {
  static commandName = 'sessions:cleanup'
  static description = 'Clean up expired and old user sessions'

  static options: CommandOptions = {
    startApp: true,
    allowUnknownFlags: false,
    staysAlive: false,
  }

  async run() {
    this.logger.info('Starting session cleanup...')

    const sessionService = new SessionService()

    try {
      // 1. Mark expired sessions
      const expiredCount = await sessionService.cleanupExpiredSessions()
      this.logger.info(`Marked ${expiredCount} expired sessions`)

      // 2. Delete old sessions (older than 90 days)
      const oldSessionsCount = await this.deleteOldSessions()
      this.logger.info(`Deleted ${oldSessionsCount} old sessions`)

      // 3. Clean up revoked sessions older than 30 days
      const revokedSessionsCount = await this.deleteOldRevokedSessions()
      this.logger.info(`Deleted ${revokedSessionsCount} old revoked sessions`)

      // 4. Generate cleanup report
      await this.generateCleanupReport()

      this.logger.success('Session cleanup completed successfully')
    } catch (error) {
      this.logger.error('Session cleanup failed:', error.message)
      this.exitCode = 1
    }
  }

  /**
   * Delete sessions older than 90 days
   */
  private async deleteOldSessions(): Promise<number> {
    const cutoffDate = DateTime.now().minus({ days: 90 })
    
    const result = await UserSession.query()
      .where('createdAt', '<', cutoffDate.toSQL())
      .delete()

    return result[0] || 0
  }

  /**
   * Delete revoked sessions older than 30 days
   */
  private async deleteOldRevokedSessions(): Promise<number> {
    const cutoffDate = DateTime.now().minus({ days: 30 })
    
    const result = await UserSession.query()
      .where('status', 'revoked')
      .where('logoutAt', '<', cutoffDate.toSQL())
      .delete()

    return result[0] || 0
  }

  /**
   * Generate cleanup report
   */
  private async generateCleanupReport(): Promise<void> {
    const stats = await UserSession.query()
      .select('status')
      .count('* as total')
      .groupBy('status')

    this.logger.info('Current session statistics:')
    
    for (const stat of stats) {
      this.logger.info(`  ${stat.status}: ${stat.total}`)
    }

    // Additional statistics
    const totalSessions = await UserSession.query().count('* as total')
    const activeSessions = await UserSession.query()
      .where('status', 'active')
      .where('expiresAt', '>', DateTime.now().toSQL())
      .count('* as total')

    this.logger.info(`Total sessions: ${totalSessions[0].total}`)
    this.logger.info(`Active sessions: ${activeSessions[0].total}`)
  }
}
