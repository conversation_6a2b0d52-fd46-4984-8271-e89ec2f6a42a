/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from './kernel.js'
import swaggerUi from 'swagger-ui-express'
import { swaggerSpec } from '#config/swagger'

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     description: Returns the health status of the API
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: ok
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 service:
 *                   type: string
 *                   example: LMS Platform API
 *                 version:
 *                   type: string
 *                   example: 1.0.0
 */
// Health check route
router.get('/health', async () => {
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'LMS Platform API',
    version: '1.0.0'
  }
})

// Swagger documentation routes
router.get('/docs', async ({ response }) => {
  const html = swaggerUi.generateHTML(swaggerSpec, {
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'LMS Platform API Documentation'
  })
  response.header('Content-Type', 'text/html')
  return html
})

router.get('/docs/swagger.json', async () => {
  return swaggerSpec
})

// API version prefix
router.group(() => {

  // Super Admin & LMS Staff Authentication Routes
  router.group(() => {
    router.post('/login', '#controllers/auth/super_admin_auth_controller.login')
    router.post('/refresh', '#controllers/auth/super_admin_auth_controller.refresh')

    // Protected routes
    router.group(() => {
      router.get('/me', '#controllers/auth/super_admin_auth_controller.me')
      router.get('/dashboard', '#controllers/auth/super_admin_auth_controller.dashboard')
      router.post('/logout', '#controllers/auth/super_admin_auth_controller.logout')
    }).use(middleware.auth({ guards: ['jwt'] }))

  }).prefix('/auth/super-admin')

  // Institute Admin & Staff Authentication Routes
  router.group(() => {
    router.post('/register', '#controllers/auth/institute_auth_controller.register')
    router.post('/login', '#controllers/auth/institute_auth_controller.login')
    router.post('/refresh', '#controllers/auth/institute_auth_controller.refresh')

    // Protected routes
    router.group(() => {
      router.get('/me', '#controllers/auth/institute_auth_controller.me')
      router.get('/dashboard', '#controllers/auth/institute_auth_controller.dashboard')
      router.post('/logout', '#controllers/auth/institute_auth_controller.logout')
    }).use(middleware.auth({ guards: ['jwt'] }))

  }).prefix('/auth/institute')

  // Student Authentication Routes
  router.group(() => {
    router.post('/login', '#controllers/auth/student_auth_controller.login')
    router.post('/refresh', '#controllers/auth/student_auth_controller.refresh')

    // Protected routes
    router.group(() => {
      router.get('/me', '#controllers/auth/student_auth_controller.me')
      router.get('/dashboard', '#controllers/auth/student_auth_controller.dashboard')
      router.post('/logout', '#controllers/auth/student_auth_controller.logout')
    }).use(middleware.auth({ guards: ['jwt'] }))

  }).prefix('/auth/student')

  // Super Admin Protected Routes
  router.group(() => {

    // Institute Management
    router.group(() => {
      router.get('/', '#controllers/super_admin/institutes_controller.index')
      router.post('/', '#controllers/super_admin/institutes_controller.store')
      router.get('/:id', '#controllers/super_admin/institutes_controller.show')
      router.put('/:id', '#controllers/super_admin/institutes_controller.update')
      router.delete('/:id', '#controllers/super_admin/institutes_controller.destroy')
      router.post('/:id/suspend', '#controllers/super_admin/institutes_controller.suspend')
      router.post('/:id/activate', '#controllers/super_admin/institutes_controller.activate')
    }).prefix('/institutes')

    // User Management
    router.group(() => {
      router.get('/', '#controllers/super_admin/users_controller.index')
      router.post('/', '#controllers/super_admin/users_controller.store')
      router.get('/:id', '#controllers/super_admin/users_controller.show')
      router.put('/:id', '#controllers/super_admin/users_controller.update')
      router.delete('/:id', '#controllers/super_admin/users_controller.destroy')
    }).prefix('/users')

    // Analytics
    router.group(() => {
      router.get('/overview', '#controllers/super_admin/analytics_controller.overview')
      router.get('/institutes', '#controllers/super_admin/analytics_controller.institutes')
      router.get('/users', '#controllers/super_admin/analytics_controller.users')
      router.get('/revenue', '#controllers/super_admin/analytics_controller.revenue')
    }).prefix('/analytics')

  }).prefix('/super-admin').use(middleware.auth({ guards: ['jwt'] }))

  // Institute Admin Protected Routes
  router.group(() => {

    // Student Management
    router.group(() => {
      router.get('/', '#controllers/institute_admin/students_controller.index')
      router.post('/', '#controllers/institute_admin/students_controller.store')
      router.get('/:id', '#controllers/institute_admin/students_controller.show')
      router.put('/:id', '#controllers/institute_admin/students_controller.update')
      router.delete('/:id', '#controllers/institute_admin/students_controller.destroy')
    }).prefix('/students')

    // Course Management
    router.group(() => {
      router.get('/', '#controllers/institute_admin/courses_controller.index')
      router.post('/', '#controllers/institute_admin/courses_controller.store')
      router.get('/:id', '#controllers/institute_admin/courses_controller.show')
      router.put('/:id', '#controllers/institute_admin/courses_controller.update')
      router.delete('/:id', '#controllers/institute_admin/courses_controller.destroy')
    }).prefix('/courses')

    // Enrollment Management
    router.group(() => {
      router.get('/', '#controllers/institute_admin/enrollments_controller.index')
      router.post('/', '#controllers/institute_admin/enrollments_controller.store')
      router.get('/:id', '#controllers/institute_admin/enrollments_controller.show')
      router.put('/:id', '#controllers/institute_admin/enrollments_controller.update')
      router.delete('/:id', '#controllers/institute_admin/enrollments_controller.destroy')
    }).prefix('/enrollments')

    // Analytics
    router.group(() => {
      router.get('/overview', '#controllers/institute_admin/analytics_controller.overview')
      router.get('/students', '#controllers/institute_admin/analytics_controller.students')
      router.get('/courses', '#controllers/institute_admin/analytics_controller.courses')
    }).prefix('/analytics')

  }).prefix('/institute-admin').use(middleware.auth({ guards: ['jwt'] }))

  // Student Protected Routes
  router.group(() => {

    // Course Access
    router.group(() => {
      router.get('/', '#controllers/student/courses_controller.index')
      router.get('/:id', '#controllers/student/courses_controller.show')
      router.get('/:id/lessons', '#controllers/student/courses_controller.lessons')
      router.post('/:id/enroll', '#controllers/student/courses_controller.enroll')
    }).prefix('/courses')

    // Assignments
    router.group(() => {
      router.get('/', '#controllers/student/assignments_controller.index')
      router.get('/:id', '#controllers/student/assignments_controller.show')
      router.post('/:id/submit', '#controllers/student/assignments_controller.submit')
    }).prefix('/assignments')

    // Grades
    router.group(() => {
      router.get('/', '#controllers/student/grades_controller.index')
      router.get('/transcript', '#controllers/student/grades_controller.transcript')
    }).prefix('/grades')

    // Profile
    router.group(() => {
      router.get('/', '#controllers/student/profile_controller.show')
      router.put('/', '#controllers/student/profile_controller.update')
      router.post('/avatar', '#controllers/student/profile_controller.uploadAvatar')
    }).prefix('/profile')

  }).prefix('/student').use(middleware.auth({ guards: ['jwt'] }))

}).prefix('/api/v1')

// Catch-all route for undefined endpoints
router.any('*', async ({ response }) => {
  return response.status(404).json({
    success: false,
    message: 'Endpoint not found',
    error: 'The requested resource does not exist'
  })
})
