import User from '#models/user';
import { loginValidator, refreshTokenValidator } from '#validators/auth_validator';
import SuperAdminAuthService from '#services/auth/super_admin_auth_service';
export default class SuperAdminAuthController {
    authService = new SuperAdminAuthService();
    async login({ request, response }) {
        try {
            const payload = await request.validateUsing(loginValidator);
            const user = await User.findBy('email', payload.email);
            if (!user) {
                return response.status(401).json({
                    success: false,
                    message: 'Invalid credentials',
                    errors: {
                        email: ['Invalid email or password']
                    }
                });
            }
            if (!['super_admin', 'lms_admin'].includes(user.role)) {
                return response.status(403).json({
                    success: false,
                    message: 'Access denied. Super admin access required.',
                    errors: {
                        role: ['Insufficient permissions']
                    }
                });
            }
            const isPasswordValid = await user.verifyPassword(payload.password);
            if (!isPasswordValid) {
                return response.status(401).json({
                    success: false,
                    message: 'Invalid credentials',
                    errors: {
                        password: ['Invalid email or password']
                    }
                });
            }
            if (user.status !== 'active') {
                return response.status(403).json({
                    success: false,
                    message: 'Account is not active',
                    errors: {
                        status: ['Your account has been suspended or deactivated']
                    }
                });
            }
            const tokens = await this.authService.generateTokens(user);
            await this.authService.updateLastLogin(user, request.ip());
            return response.status(200).json({
                success: true,
                message: 'Login successful',
                data: {
                    user: user.serialize(),
                    tokens,
                    permissions: await this.authService.getUserPermissions(user)
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async me({ auth, response }) {
        try {
            const user = auth.user;
            return response.status(200).json({
                success: true,
                data: {
                    user: user.serialize(),
                    permissions: await this.authService.getUserPermissions(user)
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async dashboard({ auth, response }) {
        try {
            const user = auth.user;
            const dashboardData = await this.authService.getDashboardData(user);
            return response.status(200).json({
                success: true,
                data: dashboardData
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async refresh({ request, response }) {
        try {
            const payload = await request.validateUsing(refreshTokenValidator);
            const tokens = await this.authService.refreshTokens(payload.refreshToken);
            return response.status(200).json({
                success: true,
                message: 'Token refreshed successfully',
                data: { tokens }
            });
        }
        catch (error) {
            return response.status(401).json({
                success: false,
                message: 'Invalid refresh token',
                error: error.message
            });
        }
    }
    async logout({ auth, response }) {
        try {
            const user = auth.user;
            await this.authService.logout(user);
            return response.status(200).json({
                success: true,
                message: 'Logged out successfully'
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
}
//# sourceMappingURL=super_admin_auth_controller.js.map