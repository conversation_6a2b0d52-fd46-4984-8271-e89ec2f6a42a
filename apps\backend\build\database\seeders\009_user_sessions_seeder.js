import { BaseSeeder } from '@adonisjs/lucid/seeders';
import { DateTime } from 'luxon';
import UserSession from '#models/user_session';
import User from '#models/user';
export default class extends BaseSeeder {
    async run() {
        const users = await User.query().limit(10);
        if (users.length === 0) {
            console.log('No users found. Please run user seeder first.');
            return;
        }
        const sessionsData = [];
        for (const user of users) {
            sessionsData.push({
                userId: user.id,
                sessionId: `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                tokenId: `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                tokenType: 'access',
                deviceType: 'desktop',
                deviceName: 'MacBook Pro',
                deviceModel: 'MacBook Pro 16-inch',
                deviceVendor: 'Apple',
                operatingSystem: 'macOS',
                osVersion: '14.0',
                isMobile: false,
                browserName: 'Chrome',
                browserVersion: '119.0',
                browserEngine: 'Blink',
                userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                ipAddress: '*************',
                country: 'United States',
                region: 'California',
                city: 'San Francisco',
                timezone: 'America/Los_Angeles',
                isp: 'Comcast Cable',
                loginAt: DateTime.now().minus({ hours: 2 }),
                lastActivityAt: DateTime.now().minus({ minutes: 5 }),
                expiresAt: DateTime.now().plus({ hours: 22 }),
                logoutAt: null,
                status: 'active',
                isCurrentSession: true,
                isTrustedDevice: true,
                isBot: false,
                isSuspicious: false,
                referrer: 'https://google.com',
                landingPage: '/dashboard',
                metadata: {
                    loginMethod: 'password',
                    clientVersion: '1.0.0',
                },
                createdAt: DateTime.now().minus({ hours: 2 }),
                updatedAt: DateTime.now().minus({ minutes: 5 }),
            });
            sessionsData.push({
                userId: user.id,
                sessionId: `sess_${Date.now() - 1000}_${Math.random().toString(36).substr(2, 9)}`,
                tokenId: `token_${Date.now() - 1000}_${Math.random().toString(36).substr(2, 9)}`,
                tokenType: 'access',
                deviceType: 'mobile',
                deviceName: 'iPhone 15',
                deviceModel: 'iPhone 15 Pro',
                deviceVendor: 'Apple',
                operatingSystem: 'iOS',
                osVersion: '17.0',
                isMobile: true,
                browserName: 'Safari',
                browserVersion: '17.0',
                browserEngine: 'WebKit',
                userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                ipAddress: '*********',
                country: 'United States',
                region: 'California',
                city: 'Los Angeles',
                timezone: 'America/Los_Angeles',
                isp: 'Verizon Wireless',
                loginAt: DateTime.now().minus({ days: 1 }),
                lastActivityAt: DateTime.now().minus({ hours: 12 }),
                expiresAt: DateTime.now().plus({ hours: 12 }),
                logoutAt: DateTime.now().minus({ hours: 12 }),
                status: 'logged_out',
                isCurrentSession: false,
                isTrustedDevice: true,
                isBot: false,
                isSuspicious: false,
                referrer: null,
                landingPage: '/login',
                metadata: {
                    loginMethod: 'password',
                    clientVersion: '1.0.0',
                },
                createdAt: DateTime.now().minus({ days: 1 }),
                updatedAt: DateTime.now().minus({ hours: 12 }),
            });
            if (Math.random() > 0.7) {
                sessionsData.push({
                    userId: user.id,
                    sessionId: `sess_${Date.now() - 2000}_${Math.random().toString(36).substr(2, 9)}`,
                    tokenId: `token_${Date.now() - 2000}_${Math.random().toString(36).substr(2, 9)}`,
                    tokenType: 'access',
                    deviceType: 'desktop',
                    deviceName: 'Unknown',
                    deviceModel: null,
                    deviceVendor: null,
                    operatingSystem: 'Linux',
                    osVersion: 'Ubuntu 20.04',
                    isMobile: false,
                    browserName: 'Firefox',
                    browserVersion: '118.0',
                    browserEngine: 'Gecko',
                    userAgent: 'Mozilla/5.0 (X11; Linux x86_64; rv:118.0) Gecko/20100101 Firefox/118.0',
                    ipAddress: '************',
                    country: 'Russia',
                    region: 'Moscow',
                    city: 'Moscow',
                    timezone: 'Europe/Moscow',
                    isp: 'Unknown ISP',
                    loginAt: DateTime.now().minus({ days: 3 }),
                    lastActivityAt: DateTime.now().minus({ days: 3 }),
                    expiresAt: DateTime.now().minus({ days: 2 }),
                    logoutAt: DateTime.now().minus({ days: 3 }),
                    status: 'revoked',
                    isCurrentSession: false,
                    isTrustedDevice: false,
                    isBot: false,
                    isSuspicious: true,
                    securityNotes: 'Login from new country - automatically revoked',
                    referrer: null,
                    landingPage: '/login',
                    metadata: {
                        loginMethod: 'password',
                        clientVersion: null,
                    },
                    createdAt: DateTime.now().minus({ days: 3 }),
                    updatedAt: DateTime.now().minus({ days: 3 }),
                });
            }
        }
        await UserSession.createMany(sessionsData);
        console.log(`✅ Created ${sessionsData.length} user sessions`);
    }
}
//# sourceMappingURL=009_user_sessions_seeder.js.map