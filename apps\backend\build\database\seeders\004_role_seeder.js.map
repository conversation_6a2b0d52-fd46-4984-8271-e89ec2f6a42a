{"version": 3, "file": "004_role_seeder.js", "sourceRoot": "", "sources": ["../../../database/seeders/004_role_seeder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,IAAI,MAAM,cAAc,CAAA;AAE/B,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IACrC,KAAK,CAAC,GAAG;QACP,MAAM,IAAI,CAAC,UAAU,CAAC;YACpB;gBACE,IAAI,EAAE,aAAa;gBACnB,YAAY,EAAE,qBAAqB;gBACnC,WAAW,EAAE,oCAAoC;gBACjD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,WAAW;gBACjB,YAAY,EAAE,mBAAmB;gBACjC,WAAW,EAAE,iCAAiC;gBAC9C,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,YAAY,EAAE,yBAAyB;gBACvC,WAAW,EAAE,kCAAkC;gBAC/C,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,YAAY,EAAE,iBAAiB;gBAC/B,WAAW,EAAE,0CAA0C;gBACvD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,YAAY,EAAE,YAAY;gBAC1B,WAAW,EAAE,4CAA4C;gBACzD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,YAAY,EAAE,SAAS;gBACvB,WAAW,EAAE,qDAAqD;gBAClE,cAAc,EAAE,IAAI;aACrB;SACF,CAAC,CAAA;IACJ,CAAC;CACF"}