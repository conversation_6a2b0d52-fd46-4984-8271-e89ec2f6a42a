import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'user_sessions';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.integer('user_id').unsigned().notNullable();
            table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
            table.string('session_id', 255).notNullable().unique();
            table.string('token_id', 255).notNullable().unique();
            table.enum('token_type', ['access', 'refresh']).notNullable();
            table.string('device_type', 50).nullable();
            table.string('device_name', 100).nullable();
            table.string('device_model', 100).nullable();
            table.string('device_vendor', 100).nullable();
            table.string('operating_system', 100).nullable();
            table.string('os_version', 50).nullable();
            table.string('browser_name', 100).nullable();
            table.string('browser_version', 50).nullable();
            table.string('browser_engine', 50).nullable();
            table.text('user_agent').nullable();
            table.string('ip_address', 45).notNullable();
            table.string('country', 100).nullable();
            table.string('region', 100).nullable();
            table.string('city', 100).nullable();
            table.string('timezone', 50).nullable();
            table.string('isp', 200).nullable();
            table.timestamp('login_at', { useTz: true }).notNullable();
            table.timestamp('last_activity_at', { useTz: true }).notNullable();
            table.timestamp('expires_at', { useTz: true }).notNullable();
            table.timestamp('logout_at', { useTz: true }).nullable();
            table.enum('status', ['active', 'expired', 'revoked', 'logged_out']).defaultTo('active');
            table.boolean('is_current_session').defaultTo(false);
            table.boolean('is_trusted_device').defaultTo(false);
            table.boolean('is_mobile').defaultTo(false);
            table.boolean('is_bot').defaultTo(false);
            table.boolean('is_suspicious').defaultTo(false);
            table.text('security_notes').nullable();
            table.json('metadata').nullable();
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.index(['user_id']);
            table.index(['session_id']);
            table.index(['token_id']);
            table.index(['status']);
            table.index(['ip_address']);
            table.index(['login_at']);
            table.index(['last_activity_at']);
            table.index(['expires_at']);
            table.index(['is_current_session']);
            table.index(['device_type']);
            table.index(['browser_name']);
            table.index(['operating_system']);
            table.index(['country']);
            table.index(['user_id', 'status']);
            table.index(['user_id', 'is_current_session']);
            table.index(['user_id', 'device_type']);
            table.index(['ip_address', 'user_id']);
            table.index(['login_at', 'user_id']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=012_create_user_sessions_table.js.map