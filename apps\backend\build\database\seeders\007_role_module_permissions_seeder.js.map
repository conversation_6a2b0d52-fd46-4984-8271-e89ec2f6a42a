{"version": 3, "file": "007_role_module_permissions_seeder.js", "sourceRoot": "", "sources": ["../../../database/seeders/007_role_module_permissions_seeder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,oBAAoB,MAAM,gCAAgC,CAAA;AACjE,OAAO,IAAI,MAAM,cAAc,CAAA;AAC/B,OAAO,MAAM,MAAM,gBAAgB,CAAA;AACnC,OAAO,UAAU,MAAM,oBAAoB,CAAA;AAC3C,OAAO,SAAS,MAAM,mBAAmB,CAAA;AAEzC,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IACrC,KAAK,CAAC,GAAG;QAEP,MAAM,aAAa,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;QAC5E,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAA;QAC1E,MAAM,GAAG,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QAGvD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;QAC7E,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;QAC7E,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAGpE,MAAM,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,CAAC,CAAA;QAG/E,MAAM,IAAI,CAAC,8BAA8B,CAAC,kBAAkB,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,CAAC,CAAA;QAGlF,MAAM,IAAI,CAAC,2BAA2B,CAAC,cAAc,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,CAAC,CAAA;QAG3E,MAAM,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,CAAA;QACzE,MAAM,IAAI,CAAC,8BAA8B,CAAC,kBAAkB,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,CAAA;QAC5E,MAAM,IAAI,CAAC,2BAA2B,CAAC,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,CAAA;QAErE,MAAM,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAA;QACrE,MAAM,IAAI,CAAC,8BAA8B,CAAC,kBAAkB,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAA;QACxE,MAAM,IAAI,CAAC,2BAA2B,CAAC,cAAc,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAA;IACnE,CAAC;IAGD,KAAK,CAAC,2BAA2B,CAAC,MAAM,EAAE,WAAW;QAEnD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QAE3D,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,oBAAoB,CAAC,MAAM,CAAC;oBAChC,OAAO,EAAE,MAAM;oBACf,SAAS,EAAE,MAAM,CAAC,EAAE;oBACpB,aAAa,EAAE,UAAU,CAAC,EAAE;oBAC5B,YAAY,EAAE,WAAW;iBAC1B,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,8BAA8B,CAAC,MAAM,EAAE,WAAW;QAEtD,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAC9D,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QAClE,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QAGpE,MAAM,mBAAmB,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAC/E,MAAM,qBAAqB,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;QACnF,MAAM,sBAAsB,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,eAAe,CAAC,CAAA;QAGrF,MAAM,oBAAoB,CAAC,UAAU,CAAC;YACpC;gBACE,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,WAAW,CAAC,EAAE;gBACzB,aAAa,EAAE,mBAAmB,CAAC,EAAE;gBACrC,YAAY,EAAE,WAAW;aAC1B;YACD;gBACE,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,aAAa,CAAC,EAAE;gBAC3B,aAAa,EAAE,qBAAqB,CAAC,EAAE;gBACvC,YAAY,EAAE,WAAW;aAC1B;YACD;gBACE,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,cAAc,CAAC,EAAE;gBAC5B,aAAa,EAAE,sBAAsB,CAAC,EAAE;gBACxC,YAAY,EAAE,WAAW;aAC1B;SACF,CAAC,CAAA;IACJ,CAAC;IAGD,KAAK,CAAC,2BAA2B,CAAC,MAAM,EAAE,WAAW;QAEnD,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QAClE,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QAGpE,MAAM,qBAAqB,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;QACnF,MAAM,qBAAqB,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;QACnF,MAAM,sBAAsB,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,eAAe,CAAC,CAAA;QAGrF,MAAM,oBAAoB,CAAC,UAAU,CAAC;YACpC;gBACE,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,aAAa,CAAC,EAAE;gBAC3B,aAAa,EAAE,qBAAqB,CAAC,EAAE;gBACvC,YAAY,EAAE,WAAW;aAC1B;YACD;gBACE,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,aAAa,CAAC,EAAE;gBAC3B,aAAa,EAAE,qBAAqB,CAAC,EAAE;gBACvC,YAAY,EAAE,WAAW;aAC1B;YACD;gBACE,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,cAAc,CAAC,EAAE;gBAC5B,aAAa,EAAE,sBAAsB,CAAC,EAAE;gBACxC,YAAY,EAAE,WAAW;aAC1B;SACF,CAAC,CAAA;IACJ,CAAC;CACF"}