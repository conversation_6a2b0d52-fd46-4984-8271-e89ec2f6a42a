import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function setupDatabase() {
  let connection

  try {
    console.log('🔄 Setting up database...')

    // Connect to MySQL server (without specifying database)
    const serverConfig = { ...dbConfig }
    delete serverConfig.database

    connection = await mysql.createConnection(serverConfig)
    console.log('✅ Connected to MySQL server')

    // Create database if it doesn't exist
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\``)
    console.log(`✅ Database '${dbConfig.database}' created/verified`)

    // Close connection and reconnect to the specific database
    await connection.end()
    connection = await mysql.createConnection(dbConfig)
    console.log(`✅ Connected to database '${dbConfig.database}'`)

    // Create institutes table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS institutes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        code VARCHAR(50) NOT NULL UNIQUE,
        slug VARCHAR(255) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NULL,
        address TEXT NULL,
        website VARCHAR(255) NULL,
        logo_url VARCHAR(500) NULL,
        favicon_url VARCHAR(500) NULL,
        type ENUM('university', 'college', 'school', 'training_center', 'coaching_institute', 'online_academy', 'corporate_training') DEFAULT 'college',
        affiliation ENUM('government', 'private', 'semi_government', 'autonomous') DEFAULT 'private',
        status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
        subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'basic',
        subscription_status ENUM('trial', 'active', 'expired', 'cancelled') DEFAULT 'trial',
        subscription_expires_at TIMESTAMP NULL,
        country_id INT NULL,
        state_id INT NULL,
        city_id INT NULL,
        timezone VARCHAR(50) DEFAULT 'UTC',
        currency VARCHAR(3) DEFAULT 'USD',
        language VARCHAR(5) DEFAULT 'en',
        max_students INT DEFAULT 100,
        max_staff INT DEFAULT 10,
        max_courses INT DEFAULT 50,
        settings JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_institutes_status (status),
        INDEX idx_institutes_subscription (subscription_plan, subscription_status),
        INDEX idx_institutes_location (country_id, state_id, city_id)
      )
    `)
    console.log('✅ Institutes table created/verified')

    // Create users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        institute_id INT NOT NULL,
        branch_id INT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        student_id VARCHAR(50) NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NULL,
        date_of_birth DATE NULL,
        gender ENUM('male', 'female', 'other') NULL,
        address TEXT NULL,
        avatar_url VARCHAR(500) NULL,
        role ENUM('super_admin', 'lms_admin', 'institute_admin', 'institute_staff', 'instructor', 'student') NOT NULL,
        status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
        email_verified_at TIMESTAMP NULL,
        last_login_at TIMESTAMP NULL,
        preferences JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
        INDEX idx_users_email (email),
        INDEX idx_users_role (role),
        INDEX idx_users_status (status),
        INDEX idx_users_institute (institute_id),
        INDEX idx_users_student_id (student_id)
      )
    `)
    console.log('✅ Users table created/verified')

    // Create branches table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS branches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        institute_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50) NOT NULL,
        description TEXT NULL,
        address TEXT NULL,
        city VARCHAR(100) NULL,
        state VARCHAR(100) NULL,
        country VARCHAR(100) NULL,
        zip_code VARCHAR(20) NULL,
        phone VARCHAR(20) NULL,
        email VARCHAR(255) NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
        UNIQUE KEY unique_institute_branch_code (institute_id, code),
        INDEX idx_branches_institute (institute_id),
        INDEX idx_branches_status (status)
      )
    `)
    console.log('✅ Branches table created/verified')

    // Create courses table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        institute_id INT NOT NULL,
        instructor_id INT NULL,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) NOT NULL,
        description TEXT NULL,
        short_description VARCHAR(500) NULL,
        thumbnail VARCHAR(500) NULL,
        price DECIMAL(10,2) DEFAULT 0.00,
        discount_price DECIMAL(10,2) NULL,
        currency VARCHAR(3) DEFAULT 'USD',
        duration INT NULL COMMENT 'Duration in hours',
        level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
        language VARCHAR(5) DEFAULT 'en',
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        max_students INT NULL,
        enrolled_students INT DEFAULT 0,
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_ratings INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_institute_course_slug (institute_id, slug),
        INDEX idx_courses_institute (institute_id),
        INDEX idx_courses_instructor (instructor_id),
        INDEX idx_courses_status (status),
        INDEX idx_courses_level (level)
      )
    `)
    console.log('✅ Courses table created/verified')

    // Create enrollments table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        course_id INT NOT NULL,
        status ENUM('enrolled', 'completed', 'dropped', 'suspended') DEFAULT 'enrolled',
        progress DECIMAL(5,2) DEFAULT 0.00,
        grade VARCHAR(5) NULL,
        enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        dropped_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_course (user_id, course_id),
        INDEX idx_enrollments_user (user_id),
        INDEX idx_enrollments_course (course_id),
        INDEX idx_enrollments_status (status)
      )
    `)
    console.log('✅ Enrollments table created/verified')

    console.log('🎉 Database setup completed successfully!')

  } catch (error) {
    console.error('❌ Database setup failed:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

setupDatabase()
