import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function setupDatabase() {
  let connection

  try {
    console.log('🔄 Setting up database...')

    // Connect to MySQL server (without specifying database)
    const serverConfig = { ...dbConfig }
    delete serverConfig.database

    connection = await mysql.createConnection(serverConfig)
    console.log('✅ Connected to MySQL server')

    // Create database if it doesn't exist
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\``)
    console.log(`✅ Database '${dbConfig.database}' created/verified`)

    // Close connection and reconnect to the specific database
    await connection.end()
    connection = await mysql.createConnection(dbConfig)
    console.log(`✅ Connected to database '${dbConfig.database}'`)

    // Create institutes table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS institutes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        code VARCHAR(50) NOT NULL UNIQUE,
        slug VARCHAR(255) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NULL,
        address TEXT NULL,
        website VARCHAR(255) NULL,
        logo_url VARCHAR(500) NULL,
        favicon_url VARCHAR(500) NULL,
        type ENUM('university', 'college', 'school', 'training_center', 'coaching_institute', 'online_academy', 'corporate_training') DEFAULT 'college',
        affiliation ENUM('government', 'private', 'semi_government', 'autonomous') DEFAULT 'private',
        status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
        subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'basic',
        subscription_status ENUM('trial', 'active', 'expired', 'cancelled') DEFAULT 'trial',
        subscription_expires_at TIMESTAMP NULL,
        country_id INT NULL,
        state_id INT NULL,
        city_id INT NULL,
        timezone VARCHAR(50) DEFAULT 'UTC',
        currency VARCHAR(3) DEFAULT 'USD',
        language VARCHAR(5) DEFAULT 'en',
        max_students INT DEFAULT 100,
        max_staff INT DEFAULT 10,
        max_courses INT DEFAULT 50,
        settings JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_institutes_status (status),
        INDEX idx_institutes_subscription (subscription_plan, subscription_status),
        INDEX idx_institutes_location (country_id, state_id, city_id)
      )
    `)
    console.log('✅ Institutes table created/verified')

    // Create users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        institute_id INT NOT NULL,
        branch_id INT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        student_id VARCHAR(50) NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NULL,
        date_of_birth DATE NULL,
        gender ENUM('male', 'female', 'other') NULL,
        address TEXT NULL,
        avatar_url VARCHAR(500) NULL,
        role ENUM('super_admin', 'lms_admin', 'institute_admin', 'institute_staff', 'instructor', 'student') NOT NULL,
        status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
        email_verified_at TIMESTAMP NULL,
        last_login_at TIMESTAMP NULL,
        preferences JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
        INDEX idx_users_email (email),
        INDEX idx_users_role (role),
        INDEX idx_users_status (status),
        INDEX idx_users_institute (institute_id),
        INDEX idx_users_student_id (student_id)
      )
    `)
    console.log('✅ Users table created/verified')

    // Create branches table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS branches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        institute_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50) NOT NULL,
        description TEXT NULL,
        address TEXT NULL,
        city VARCHAR(100) NULL,
        state VARCHAR(100) NULL,
        country VARCHAR(100) NULL,
        zip_code VARCHAR(20) NULL,
        phone VARCHAR(20) NULL,
        email VARCHAR(255) NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
        UNIQUE KEY unique_institute_branch_code (institute_id, code),
        INDEX idx_branches_institute (institute_id),
        INDEX idx_branches_status (status)
      )
    `)
    console.log('✅ Branches table created/verified')

    // Create courses table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        institute_id INT NOT NULL,
        instructor_id INT NULL,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) NOT NULL,
        description TEXT NULL,
        short_description VARCHAR(500) NULL,
        thumbnail VARCHAR(500) NULL,
        price DECIMAL(10,2) DEFAULT 0.00,
        discount_price DECIMAL(10,2) NULL,
        currency VARCHAR(3) DEFAULT 'USD',
        duration INT NULL COMMENT 'Duration in hours',
        level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
        language VARCHAR(5) DEFAULT 'en',
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        max_students INT NULL,
        enrolled_students INT DEFAULT 0,
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_ratings INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_institute_course_slug (institute_id, slug),
        INDEX idx_courses_institute (institute_id),
        INDEX idx_courses_instructor (instructor_id),
        INDEX idx_courses_status (status),
        INDEX idx_courses_level (level)
      )
    `)
    console.log('✅ Courses table created/verified')

    // Create enrollments table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        course_id INT NOT NULL,
        status ENUM('enrolled', 'completed', 'dropped', 'suspended') DEFAULT 'enrolled',
        progress DECIMAL(5,2) DEFAULT 0.00,
        grade VARCHAR(5) NULL,
        enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        dropped_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_course (user_id, course_id),
        INDEX idx_enrollments_user (user_id),
        INDEX idx_enrollments_course (course_id),
        INDEX idx_enrollments_status (status)
      )
    `)
    console.log('✅ Enrollments table created/verified')

    // Create modules table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS modules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        description VARCHAR(255) NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)
    console.log('✅ Modules table created/verified')

    // Create roles table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        description VARCHAR(255) NULL,
        is_system_role BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_roles_system (is_system_role)
      )
    `)
    console.log('✅ Roles table created/verified')

    // Create permissions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        description VARCHAR(255) NULL,
        module_id INT UNSIGNED NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (module_id) REFERENCES modules(id) ON DELETE CASCADE,
        INDEX idx_permissions_module (module_id)
      )
    `)
    console.log('✅ Permissions table created/verified')

    // Create institute_module_access table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS institute_module_access (
        id INT AUTO_INCREMENT PRIMARY KEY,
        institute_id INT UNSIGNED NOT NULL,
        module_id INT UNSIGNED NOT NULL,
        is_enabled BOOLEAN DEFAULT TRUE,
        settings JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
        FOREIGN KEY (module_id) REFERENCES modules(id) ON DELETE CASCADE,
        UNIQUE KEY unique_institute_module (institute_id, module_id),
        INDEX idx_institute_module_institute (institute_id),
        INDEX idx_institute_module_module (module_id)
      )
    `)
    console.log('✅ Institute Module Access table created/verified')

    // Create role_module_permissions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS role_module_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        role_id INT UNSIGNED NOT NULL,
        module_id INT UNSIGNED NOT NULL,
        permission_id INT UNSIGNED NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (module_id) REFERENCES modules(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
        UNIQUE KEY unique_role_module_permission (role_id, module_id, permission_id),
        INDEX idx_role_module_perm_role (role_id),
        INDEX idx_role_module_perm_module (module_id),
        INDEX idx_role_module_perm_permission (permission_id)
      )
    `)
    console.log('✅ Role Module Permissions table created/verified')

    // Create user_roles table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT UNSIGNED NOT NULL,
        role_id INT UNSIGNED NOT NULL,
        institute_id INT UNSIGNED NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (institute_id) REFERENCES institutes(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_role_institute (user_id, role_id, institute_id),
        INDEX idx_user_roles_user (user_id),
        INDEX idx_user_roles_role (role_id),
        INDEX idx_user_roles_institute (institute_id)
      )
    `)
    console.log('✅ User Roles table created/verified')

    // Create user_sessions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT UNSIGNED NOT NULL,
        session_id VARCHAR(255) NOT NULL UNIQUE,
        token_id VARCHAR(255) NOT NULL UNIQUE,
        token_type ENUM('access', 'refresh') NOT NULL,

        -- Device and Browser Information
        device_type VARCHAR(50) NULL,
        device_name VARCHAR(100) NULL,
        device_model VARCHAR(100) NULL,
        device_vendor VARCHAR(100) NULL,
        operating_system VARCHAR(100) NULL,
        os_version VARCHAR(50) NULL,

        -- Browser Information
        browser_name VARCHAR(100) NULL,
        browser_version VARCHAR(50) NULL,
        browser_engine VARCHAR(50) NULL,
        user_agent TEXT NULL,

        -- Network Information
        ip_address VARCHAR(45) NOT NULL,
        country VARCHAR(100) NULL,
        region VARCHAR(100) NULL,
        city VARCHAR(100) NULL,
        timezone VARCHAR(50) NULL,
        isp VARCHAR(200) NULL,

        -- Session Details
        login_at TIMESTAMP NOT NULL,
        last_activity_at TIMESTAMP NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        logout_at TIMESTAMP NULL,

        -- Session Status and Security
        status ENUM('active', 'expired', 'revoked', 'logged_out') DEFAULT 'active',
        is_current_session BOOLEAN DEFAULT FALSE,
        is_trusted_device BOOLEAN DEFAULT FALSE,
        is_mobile BOOLEAN DEFAULT FALSE,
        is_bot BOOLEAN DEFAULT FALSE,

        -- Security flags
        is_suspicious BOOLEAN DEFAULT FALSE,
        security_notes TEXT NULL,

        -- Additional metadata
        metadata JSON NULL,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

        -- Indexes for performance
        INDEX idx_user_sessions_user (user_id),
        INDEX idx_user_sessions_session (session_id),
        INDEX idx_user_sessions_token (token_id),
        INDEX idx_user_sessions_status (status),
        INDEX idx_user_sessions_ip (ip_address),
        INDEX idx_user_sessions_login (login_at),
        INDEX idx_user_sessions_activity (last_activity_at),
        INDEX idx_user_sessions_expires (expires_at),
        INDEX idx_user_sessions_current (is_current_session),
        INDEX idx_user_sessions_device (device_type),
        INDEX idx_user_sessions_browser (browser_name),
        INDEX idx_user_sessions_os (operating_system),
        INDEX idx_user_sessions_country (country),

        -- Composite indexes
        INDEX idx_user_sessions_user_status (user_id, status),
        INDEX idx_user_sessions_user_current (user_id, is_current_session),
        INDEX idx_user_sessions_user_device (user_id, device_type),
        INDEX idx_user_sessions_ip_user (ip_address, user_id),
        INDEX idx_user_sessions_login_user (login_at, user_id)
      )
    `)
    console.log('✅ User Sessions table created/verified')

    console.log('🎉 Database setup completed successfully!')

  } catch (error) {
    console.error('❌ Database setup failed:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

setupDatabase()
