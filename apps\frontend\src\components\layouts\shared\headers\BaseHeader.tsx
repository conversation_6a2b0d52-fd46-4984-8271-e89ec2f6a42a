"use client"

import { But<PERSON> } from '@/components/ui/button'
import { ThemeSwitcher } from '@/components/theme-switcher'
import {
  Men<PERSON>,
  <PERSON>,
  Search,
  ChevronDown
} from 'lucide-react'
import { SearchBar } from './SearchBar'
import { NotificationDropdown } from './NotificationDropdown'
import { UserProfileDropdown } from './UserProfileDropdown'

interface BaseHeaderProps {
  onMenuToggle: () => void
  showMenuButton?: boolean
  title?: string
  user?: {
    name: string
    email: string
    role: string
    avatar?: string
  }
  notifications?: Array<{
    id: string
    title: string
    message: string
    time: string
    read: boolean
    type: 'info' | 'warning' | 'error' | 'success'
  }>
  onLogout?: () => void
  className?: string
}

export function BaseHeader({
  onMenuToggle,
  showMenuButton = true,
  title,
  user = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin'
  },
  notifications = [],
  onLogout,
  className = ''
}: BaseHeaderProps) {
  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <header className={`bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between h-16 px-6">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {showMenuButton && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuToggle}
              className="lg:hidden"
              aria-label="Toggle menu"
            >
              <Menu className="h-5 w-5" />
            </Button>
          )}

          {title && (
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              {title}
            </h1>
          )}

          {/* Search Bar - Hidden on mobile */}
          <div className="hidden md:block">
            <SearchBar />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-4">
          {/* Mobile Search Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            aria-label="Search"
          >
            <Search className="h-5 w-5" />
          </Button>

          {/* Theme Switcher */}
          <ThemeSwitcher />

          {/* Notifications */}
          <NotificationDropdown
            notifications={notifications}
            unreadCount={unreadCount}
          />

          {/* User Profile */}
          <UserProfileDropdown user={user} onLogout={onLogout} />
        </div>
      </div>
    </header>
  )
}
