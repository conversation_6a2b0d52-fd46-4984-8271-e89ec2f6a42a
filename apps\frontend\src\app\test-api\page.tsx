'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAppDispatch, useAppSelector } from '@/store'
import { registerInstitute, loginInstitute, getCurrentUser, logoutUser } from '@/store/slices/authSlice'

export default function TestApiPage() {
  const dispatch = useAppDispatch()
  const { user, institute, isLoading, error, isAuthenticated } = useAppSelector((state) => state.auth)

  const [registerData, setRegisterData] = useState({
    instituteName: 'Test Institute',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    password: 'password123',
    confirmPassword: 'password123'
  })

  const [loginData, setLoginData] = useState({
    email: '<EMAIL>',
    password: 'password123'
  })

  const handleRegister = async () => {
    await dispatch(registerInstitute(registerData))
  }

  const handleLogin = async () => {
    await dispatch(loginInstitute(loginData))
  }

  const handleGetCurrentUser = async () => {
    await dispatch(getCurrentUser())
  }

  const handleLogout = async () => {
    await dispatch(logoutUser())
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">API Test Page</h1>
      
      {/* Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Authentication Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
            {error && <p className="text-red-600"><strong>Error:</strong> {error}</p>}
            {user && (
              <div>
                <p><strong>User:</strong> {user.first_name} {user.last_name} ({user.email})</p>
                <p><strong>Role:</strong> {user.user_type}</p>
                <p><strong>Status:</strong> {user.status}</p>
              </div>
            )}
            {institute && (
              <div>
                <p><strong>Institute:</strong> {institute.name}</p>
                <p><strong>Institute Status:</strong> {institute.status}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Register */}
        <Card>
          <CardHeader>
            <CardTitle>Register Institute</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="instituteName">Institute Name</Label>
              <Input
                id="instituteName"
                value={registerData.instituteName}
                onChange={(e) => setRegisterData(prev => ({ ...prev, instituteName: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={registerData.firstName}
                onChange={(e) => setRegisterData(prev => ({ ...prev, firstName: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={registerData.lastName}
                onChange={(e) => setRegisterData(prev => ({ ...prev, lastName: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={registerData.email}
                onChange={(e) => setRegisterData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={registerData.password}
                onChange={(e) => setRegisterData(prev => ({ ...prev, password: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={registerData.confirmPassword}
                onChange={(e) => setRegisterData(prev => ({ ...prev, confirmPassword: e.target.value }))}
              />
            </div>
            <Button onClick={handleRegister} disabled={isLoading} className="w-full">
              Register
            </Button>
          </CardContent>
        </Card>

        {/* Login */}
        <Card>
          <CardHeader>
            <CardTitle>Login</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="loginEmail">Email</Label>
              <Input
                id="loginEmail"
                type="email"
                value={loginData.email}
                onChange={(e) => setLoginData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="loginPassword">Password</Label>
              <Input
                id="loginPassword"
                type="password"
                value={loginData.password}
                onChange={(e) => setLoginData(prev => ({ ...prev, password: e.target.value }))}
              />
            </div>
            <Button onClick={handleLogin} disabled={isLoading} className="w-full">
              Login
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button onClick={handleGetCurrentUser} disabled={isLoading}>
              Get Current User
            </Button>
            <Button onClick={handleLogout} disabled={isLoading} variant="destructive">
              Logout
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
