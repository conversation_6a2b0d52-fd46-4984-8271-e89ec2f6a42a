{"name": "lms-platform", "version": "1.0.0", "description": "Multi-Tenant LMS SAAS Platform", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "dev:frontend": "turbo run dev --filter=@lms/frontend", "dev:backend": "turbo run dev --filter=@lms/backend", "build": "turbo run build", "build:frontend": "turbo run build --filter=@lms/frontend", "build:backend": "turbo run build --filter=@lms/backend", "start": "turbo run start", "start:frontend": "turbo run start --filter=@lms/frontend", "start:backend": "turbo run start --filter=@lms/backend", "lint": "turbo run lint", "lint:frontend": "turbo run lint --filter=@lms/frontend", "lint:backend": "turbo run lint --filter=@lms/backend", "test": "turbo run test", "test:frontend": "turbo run test --filter=@lms/frontend", "test:backend": "turbo run test --filter=@lms/backend", "typecheck": "turbo run typecheck", "clean": "turbo run clean && rimraf node_modules apps/*/node_modules packages/*/node_modules", "setup": "npm install && npm run setup:frontend && npm run setup:backend", "setup:frontend": "cd apps/frontend && npm install", "setup:backend": "cd apps/backend && npm install", "db:migrate": "turbo run db:migrate --filter=@lms/backend", "db:seed": "turbo run db:seed --filter=@lms/backend", "db:fresh": "turbo run db:fresh --filter=@lms/backend", "db:rollback": "turbo run db:rollback --filter=@lms/backend"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5", "turbo": "^2.5.4"}, "packageManager": "npm@10.0.0", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "author": "LMS Platform Team", "license": "MIT", "dependencies": {"@adonisjs/assembler": "^7.8.2"}}