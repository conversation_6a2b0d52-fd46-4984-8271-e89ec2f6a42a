import { DateTime } from 'luxon';
import jwt from 'jsonwebtoken';
import env from '#start/env';
import User from '#models/user';
import Enrollment from '#models/enrollment';
export default class StudentAuthService {
    async generateTokens(user) {
        const payload = {
            userId: user.id,
            studentId: user.studentId,
            role: user.role,
            instituteId: user.instituteId,
            branchId: user.branchId,
            type: 'access'
        };
        const accessToken = jwt.sign(payload, env.get('JWT_SECRET'), {
            expiresIn: env.get('JWT_EXPIRES_IN', '24h'),
            issuer: 'lms-platform',
            audience: 'lms-users'
        });
        const refreshPayload = {
            userId: user.id,
            type: 'refresh'
        };
        const refreshToken = jwt.sign(refreshPayload, env.get('JWT_SECRET'), {
            expiresIn: env.get('JWT_REFRESH_EXPIRES_IN', '7d'),
            issuer: 'lms-platform',
            audience: 'lms-users'
        });
        return {
            accessToken,
            refreshToken,
            tokenType: 'Bearer',
            expiresIn: env.get('JWT_EXPIRES_IN', '24h')
        };
    }
    async refreshTokens(refreshToken) {
        try {
            const decoded = jwt.verify(refreshToken, env.get('JWT_SECRET'));
            if (decoded.type !== 'refresh') {
                throw new Error('Invalid token type');
            }
            const user = await User.find(decoded.userId);
            if (!user || user.role !== 'student') {
                throw new Error('User not found or invalid role');
            }
            return this.generateTokens(user);
        }
        catch (error) {
            throw new Error('Invalid refresh token');
        }
    }
    async updateLastLogin(user, ipAddress) {
        user.lastLoginAt = DateTime.now();
        user.lastLoginIp = ipAddress;
        await user.save();
    }
    async getUserPermissions(user) {
        return [
            'profile.read',
            'profile.update',
            'courses.read',
            'enrollments.read',
            'assignments.read',
            'assignments.submit',
            'grades.read',
            'discussions.read',
            'discussions.create',
            'resources.read'
        ];
    }
    async getDashboardData(user) {
        if (!user.instituteId) {
            throw new Error('User not associated with any institute');
        }
        const [enrollments, completedCourses, pendingAssignments, recentGrades, progressStats] = await Promise.all([
            this.getStudentEnrollments(user.id),
            this.getCompletedCourses(user.id),
            this.getPendingAssignments(user.id),
            this.getRecentGrades(user.id),
            this.getProgressStats(user.id)
        ]);
        return {
            stats: {
                totalEnrollments: enrollments.length,
                completedCourses: completedCourses.length,
                pendingAssignments: pendingAssignments.length,
                averageGrade: progressStats.averageGrade,
                totalProgress: progressStats.totalProgress
            },
            enrollments,
            completedCourses,
            pendingAssignments,
            recentGrades
        };
    }
    async getStudentEnrollments(userId) {
        const enrollments = await Enrollment.query()
            .where('user_id', userId)
            .where('status', 'enrolled')
            .preload('course', (query) => {
            query.select('id', 'title', 'description', 'thumbnail', 'instructor_id');
            query.preload('instructor', (instructorQuery) => {
                instructorQuery.select('id', 'first_name', 'last_name');
            });
        })
            .orderBy('created_at', 'desc');
        return enrollments.map(enrollment => ({
            id: enrollment.id,
            course: {
                id: enrollment.course.id,
                title: enrollment.course.title,
                description: enrollment.course.description,
                thumbnail: enrollment.course.thumbnail,
                instructor: enrollment.course.instructor ? {
                    id: enrollment.course.instructor.id,
                    name: enrollment.course.instructor.fullName
                } : null
            },
            progress: enrollment.progress,
            grade: enrollment.grade,
            enrolledAt: enrollment.enrolledAt,
            status: enrollment.status
        }));
    }
    async getCompletedCourses(userId) {
        const completedEnrollments = await Enrollment.query()
            .where('user_id', userId)
            .where('status', 'completed')
            .preload('course', (query) => {
            query.select('id', 'title', 'thumbnail');
        })
            .orderBy('completed_at', 'desc')
            .limit(5);
        return completedEnrollments.map(enrollment => ({
            id: enrollment.id,
            course: {
                id: enrollment.course.id,
                title: enrollment.course.title,
                thumbnail: enrollment.course.thumbnail
            },
            grade: enrollment.grade,
            completedAt: enrollment.completedAt
        }));
    }
    async getPendingAssignments(userId) {
        return [
            {
                id: 1,
                title: 'Programming Project #3',
                course: 'Introduction to Programming',
                dueDate: DateTime.now().plus({ days: 3 }),
                priority: 'high'
            },
            {
                id: 2,
                title: 'Calculus Problem Set 7',
                course: 'Calculus I',
                dueDate: DateTime.now().plus({ days: 5 }),
                priority: 'medium'
            }
        ];
    }
    async getRecentGrades(userId) {
        return [
            {
                id: 1,
                assignment: 'Midterm Exam',
                course: 'Programming',
                grade: 'A',
                points: '95/100',
                gradedAt: DateTime.now().minus({ days: 2 })
            },
            {
                id: 2,
                assignment: 'Quiz 5',
                course: 'Calculus',
                grade: 'B+',
                points: '87/100',
                gradedAt: DateTime.now().minus({ days: 5 })
            }
        ];
    }
    async getProgressStats(userId) {
        const enrollments = await Enrollment.query()
            .where('user_id', userId)
            .whereIn('status', ['enrolled', 'completed']);
        if (enrollments.length === 0) {
            return {
                averageGrade: 0,
                totalProgress: 0
            };
        }
        const totalProgress = enrollments.reduce((sum, enrollment) => sum + enrollment.progress, 0);
        const averageProgress = totalProgress / enrollments.length;
        const gradePoints = enrollments
            .filter(e => e.grade)
            .map(e => this.gradeToPoints(e.grade));
        const averageGrade = gradePoints.length > 0
            ? gradePoints.reduce((sum, points) => sum + points, 0) / gradePoints.length
            : 0;
        return {
            averageGrade: Math.round(averageGrade * 10) / 10,
            totalProgress: Math.round(averageProgress * 10) / 10
        };
    }
    gradeToPoints(grade) {
        const gradeMap = {
            'A+': 4.0, 'A': 4.0, 'A-': 3.7,
            'B+': 3.3, 'B': 3.0, 'B-': 2.7,
            'C+': 2.3, 'C': 2.0, 'C-': 1.7,
            'D+': 1.3, 'D': 1.0, 'F': 0.0
        };
        return gradeMap[grade] || 0;
    }
    async logout(user) {
        user.updatedAt = DateTime.now();
        await user.save();
    }
}
//# sourceMappingURL=student_auth_service.js.map