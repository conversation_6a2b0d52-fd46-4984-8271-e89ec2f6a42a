"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAppDispatch, useAppSelector } from '@/store'
import { registerInstitute, clearError } from '@/store/slices/authSlice'
import {
  Building2,
  Eye,
  EyeOff,
  Loader2,
  User,
  GraduationCap,
  CheckCircle,
  BookOpen,
  Users,
  Award,
  TrendingUp,
  Shield,
  Zap,
  Globe
} from 'lucide-react'

export default function InstituteRegisterPage() {
  const dispatch = useAppDispatch()
  const { isLoading, error, isAuthenticated } = useAppSelector((state) => state.auth)
  const router = useRouter()

  const [formData, setFormData] = useState({
    instituteName: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  })

  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/institute-admin')
    }
  }, [isAuthenticated, router])

  // Clear error when component mounts
  useEffect(() => {
    dispatch(clearError())
  }, [dispatch])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateStep1 = () => {
    const { instituteName, firstName, lastName } = formData
    return instituteName && firstName && lastName
  }

  const validateStep2 = () => {
    const { email, password, confirmPassword } = formData
    return email && password && confirmPassword && password === confirmPassword && password.length >= 8
  }

  const handleNextStep = () => {
    if (validateStep1()) {
      setCurrentStep(2)
      dispatch(clearError())
    }
  }

  const handlePrevStep = () => {
    setCurrentStep(1)
    dispatch(clearError())
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (currentStep === 1) {
      handleNextStep()
      return
    }

    if (!validateStep2()) {
      return
    }

    try {
      const result = await dispatch(registerInstitute(formData))

      if (registerInstitute.fulfilled.match(result)) {
        // Registration successful, redirect to dashboard
        router.push('/institute-admin')
      }
    } catch (err) {
      // Error is handled by Redux
      console.error('Registration failed:', err)
    }
  }

  const features = [
    {
      icon: BookOpen,
      title: "Course Management",
      description: "Create and manage comprehensive courses with multimedia content"
    },
    {
      icon: Users,
      title: "Student Analytics",
      description: "Track student progress and performance with detailed analytics"
    },
    {
      icon: Award,
      title: "Certification System",
      description: "Issue digital certificates and badges for course completion"
    },
    {
      icon: TrendingUp,
      title: "Performance Insights",
      description: "Get insights into learning patterns and institutional metrics"
    },
    {
      icon: Shield,
      title: "Secure Platform",
      description: "Enterprise-grade security with data protection compliance"
    },
    {
      icon: Zap,
      title: "Easy Integration",
      description: "Seamlessly integrate with existing systems and tools"
    }
  ]

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
      <div className="flex h-screen">
        {/* Left Side - Marketing Content */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

          {/* Content */}
          <div className="relative z-10 flex flex-col justify-center px-8 py-8 text-white">
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl mb-4">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-3xl font-bold mb-3 leading-tight">
                Transform Your Educational Institution
              </h1>
              <p className="text-lg text-blue-100 mb-6 leading-relaxed">
                Join thousands of institutions worldwide using our comprehensive learning management system.
              </p>
            </div>

            {/* Features Grid - Compact */}
            <div className="grid grid-cols-1 gap-3 mb-6">
              {features.slice(0, 4).map((feature, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg">
                  <div className="flex-shrink-0">
                    <feature.icon className="h-5 w-5 text-blue-200" />
                  </div>
                  <div>
                    <h3 className="font-medium text-white text-sm">{feature.title}</h3>
                    <p className="text-xs text-blue-100">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Stats - Compact */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">10K+</div>
                <div className="text-xs text-blue-200">Institutions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">1M+</div>
                <div className="text-xs text-blue-200">Students</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">50K+</div>
                <div className="text-xs text-blue-200">Courses</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Registration Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-4 lg:p-6">
          <div className="w-full max-w-sm">
            {/* Header - Compact */}
            <div className="text-center mb-6">
              <div className="lg:hidden inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl mb-3">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
                {currentStep === 1 ? 'Institute Details' : 'Account Setup'}
              </h2>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                Step {currentStep} of 2: {currentStep === 1 ? 'Basic Information' : 'Login Credentials'}
              </p>
            </div>

            {/* Registration Form - Compact */}
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm dark:bg-slate-800/80">
              <CardContent className="p-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  {error && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg text-sm flex items-center">
                      <Shield className="h-4 w-4 mr-2 flex-shrink-0" />
                      {error}
                    </div>
                  )}

                  {/* Step 1: Institute Details */}
                  {currentStep === 1 && (
                    <>
                      {/* Institute Name */}
                      <div className="space-y-1">
                        <Label htmlFor="instituteName" className="text-xs font-medium text-slate-700 dark:text-slate-300">
                          Institute Name *
                        </Label>
                        <div className="relative">
                          <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            id="instituteName"
                            type="text"
                            placeholder="Harvard University"
                            value={formData.instituteName}
                            onChange={(e) => handleInputChange('instituteName', e.target.value)}
                            required
                            className="pl-9 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                      </div>

                      {/* First Name */}
                      <div className="space-y-1">
                        <Label htmlFor="firstName" className="text-xs font-medium text-slate-700 dark:text-slate-300">
                          First Name *
                        </Label>
                        <div className="relative">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            id="firstName"
                            type="text"
                            placeholder="John"
                            value={formData.firstName}
                            onChange={(e) => handleInputChange('firstName', e.target.value)}
                            required
                            className="pl-9 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                      </div>

                      {/* Last Name */}
                      <div className="space-y-1">
                        <Label htmlFor="lastName" className="text-xs font-medium text-slate-700 dark:text-slate-300">
                          Last Name *
                        </Label>
                        <div className="relative">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            id="lastName"
                            type="text"
                            placeholder="Doe"
                            value={formData.lastName}
                            onChange={(e) => handleInputChange('lastName', e.target.value)}
                            required
                            className="pl-9 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                      </div>
                    </>
                  )}

                  {/* Step 2: Account Setup */}
                  {currentStep === 2 && (
                    <>
                      {/* Email */}
                      <div className="space-y-1">
                        <Label htmlFor="email" className="text-xs font-medium text-slate-700 dark:text-slate-300">
                          Email Address *
                        </Label>
                        <div className="relative">
                          <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            required
                            className="pl-9 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                      </div>

                      {/* Password */}
                      <div className="space-y-1">
                        <Label htmlFor="password" className="text-xs font-medium text-slate-700 dark:text-slate-300">
                          Password *
                        </Label>
                        <div className="relative">
                          <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            id="password"
                            type={showPassword ? "text" : "password"}
                            placeholder="Enter password (min. 8 characters)"
                            value={formData.password}
                            onChange={(e) => handleInputChange('password', e.target.value)}
                            required
                            className="pl-9 pr-10 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>

                      {/* Confirm Password */}
                      <div className="space-y-1">
                        <Label htmlFor="confirmPassword" className="text-xs font-medium text-slate-700 dark:text-slate-300">
                          Confirm Password *
                        </Label>
                        <div className="relative">
                          <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            id="confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            placeholder="Confirm your password"
                            value={formData.confirmPassword}
                            onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                            required
                            className="pl-9 pr-10 h-10 text-sm border-slate-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                          >
                            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>

                      {/* Password Match Indicator */}
                      {formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword && (
                        <p className="text-xs text-red-600 dark:text-red-400 flex items-center">
                          <Shield className="h-3 w-3 mr-1" />
                          Passwords do not match
                        </p>
                      )}
                    </>
                  )}

                  {/* Navigation Buttons */}
                  <div className="flex space-x-3">
                    {currentStep === 2 && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handlePrevStep}
                        className="flex-1 h-10 text-sm"
                      >
                        Back
                      </Button>
                    )}

                    <Button
                      type="submit"
                      disabled={isLoading || (currentStep === 1 ? !validateStep1() : !validateStep2())}
                      className={`${currentStep === 2 ? 'flex-1' : 'w-full'} h-10 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium text-sm rounded-lg shadow-lg hover:shadow-xl transition-all duration-200`}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating Account...
                        </>
                      ) : currentStep === 1 ? (
                        'Next Step'
                      ) : (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Register
                        </>
                      )}
                    </Button>
                  </div>

                  {/* Login Link */}
                  <div className="text-center pt-3">
                    <p className="text-xs text-slate-600 dark:text-slate-400">
                      Already have an account?{' '}
                      <Link
                        href="/auth-login/login"
                        className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 transition-colors"
                      >
                        Sign in here
                      </Link>
                    </p>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
