import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_sessions'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      
      // User reference
      table.integer('user_id').unsigned().notNullable()
      table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE')
      
      // Session identification
      table.string('session_id', 255).notNullable().unique()
      table.string('token_id', 255).notNullable().unique() // JWT token ID
      table.enum('token_type', ['access', 'refresh']).notNullable()
      
      // Device and Browser Information
      table.string('device_type', 50).nullable() // mobile, desktop, tablet
      table.string('device_name', 100).nullable() // iPhone 14, Samsung Galaxy S23, etc.
      table.string('device_model', 100).nullable()
      table.string('device_vendor', 100).nullable() // Apple, Samsung, etc.
      table.string('operating_system', 100).nullable() // iOS 17.0, Android 13, Windows 11
      table.string('os_version', 50).nullable()
      
      // Browser Information
      table.string('browser_name', 100).nullable() // Chrome, Safari, Firefox
      table.string('browser_version', 50).nullable()
      table.string('browser_engine', 50).nullable() // WebKit, Blink, Gecko
      table.text('user_agent').nullable() // Full user agent string
      
      // Network Information
      table.string('ip_address', 45).notNullable() // IPv4 or IPv6
      table.string('country', 100).nullable()
      table.string('region', 100).nullable()
      table.string('city', 100).nullable()
      table.string('timezone', 50).nullable()
      table.string('isp', 200).nullable() // Internet Service Provider
      
      // Session Details
      table.timestamp('login_at', { useTz: true }).notNullable()
      table.timestamp('last_activity_at', { useTz: true }).notNullable()
      table.timestamp('expires_at', { useTz: true }).notNullable()
      table.timestamp('logout_at', { useTz: true }).nullable()
      
      // Session Status and Security
      table.enum('status', ['active', 'expired', 'revoked', 'logged_out']).defaultTo('active')
      table.boolean('is_current_session').defaultTo(false) // Mark the current active session
      table.boolean('is_trusted_device').defaultTo(false)
      table.boolean('is_mobile').defaultTo(false)
      table.boolean('is_bot').defaultTo(false)
      
      // Security flags
      table.boolean('is_suspicious').defaultTo(false)
      table.text('security_notes').nullable() // Notes about suspicious activity
      
      // Additional metadata
      table.json('metadata').nullable() // Store additional session data
   
      
      // Timestamps
      table.timestamp('created_at', { useTz: true }).defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).defaultTo(this.now())
      
      // Indexes for performance
      table.index(['user_id'])
      table.index(['session_id'])
      table.index(['token_id'])
      table.index(['status'])
      table.index(['ip_address'])
      table.index(['login_at'])
      table.index(['last_activity_at'])
      table.index(['expires_at'])
      table.index(['is_current_session'])
      table.index(['device_type'])
      table.index(['browser_name'])
      table.index(['operating_system'])
      table.index(['country'])
      
      // Composite indexes
      table.index(['user_id', 'status'])
      table.index(['user_id', 'is_current_session'])
      table.index(['user_id', 'device_type'])
      table.index(['ip_address', 'user_id'])
      table.index(['login_at', 'user_id'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
