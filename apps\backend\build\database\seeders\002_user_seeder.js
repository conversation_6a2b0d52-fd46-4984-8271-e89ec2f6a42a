import { BaseSeeder } from '@adonisjs/lucid/seeders';
import User from '#models/user';
import Institute from '#models/institute';
import hash from '@adonisjs/core/services/hash';
export default class extends BaseSeeder {
    async run() {
        const demoInstitute = await Institute.findByOrFail('slug', 'demo-institute');
        const harvard = await Institute.findByOrFail('slug', 'harvard-university');
        const mit = await Institute.findByOrFail('slug', 'mit');
        await User.create({
            email: '<EMAIL>',
            password: await hash.make('admin123'),
            first_name: 'Super',
            last_name: 'Admin',
            user_type: 'super_admin',
            status: 'active',
            email_verified_at: new Date()
        });
        await User.create({
            email: '<EMAIL>',
            password: await hash.make('lms123'),
            first_name: '<PERSON><PERSON>',
            last_name: 'Admin',
            user_type: 'lms_admin',
            status: 'active',
            email_verified_at: new Date()
        });
        await User.createMany([
            {
                email: '<EMAIL>',
                password: await hash.make('admin123'),
                first_name: 'Demo',
                last_name: 'Admin',
                user_type: 'institute_admin',
                status: 'active',
                institute_id: demoInstitute.id,
                email_verified_at: new Date()
            },
            {
                email: '<EMAIL>',
                password: await hash.make('harvard123'),
                first_name: 'Harvard',
                last_name: 'Admin',
                user_type: 'institute_admin',
                status: 'active',
                institute_id: harvard.id,
                email_verified_at: new Date()
            },
            {
                email: '<EMAIL>',
                password: await hash.make('mit123'),
                first_name: 'MIT',
                last_name: 'Admin',
                user_type: 'institute_admin',
                status: 'active',
                institute_id: mit.id,
                email_verified_at: new Date()
            }
        ]);
        await User.createMany([
            {
                email: '<EMAIL>',
                password: await hash.make('staff123'),
                first_name: 'Demo',
                last_name: 'Staff',
                user_type: 'institute_staff',
                status: 'active',
                institute_id: demoInstitute.id,
                email_verified_at: new Date()
            },
            {
                email: '<EMAIL>',
                password: await hash.make('instructor123'),
                first_name: 'John',
                last_name: 'Instructor',
                user_type: 'institute_staff',
                status: 'active',
                institute_id: demoInstitute.id,
                email_verified_at: new Date()
            }
        ]);
        await User.createMany([
            {
                email: '<EMAIL>',
                password: await hash.make('student123'),
                first_name: 'John',
                last_name: 'Doe',
                user_type: 'student',
                status: 'active',
                institute_id: demoInstitute.id,
                email_verified_at: new Date()
            },
            {
                email: '<EMAIL>',
                password: await hash.make('student123'),
                first_name: 'Jane',
                last_name: 'Smith',
                user_type: 'student',
                status: 'active',
                institute_id: demoInstitute.id,
                email_verified_at: new Date()
            },
            {
                email: '<EMAIL>',
                password: await hash.make('student123'),
                first_name: 'Bob',
                last_name: 'Johnson',
                user_type: 'student',
                status: 'active',
                institute_id: demoInstitute.id,
                email_verified_at: new Date()
            },
            {
                email: '<EMAIL>',
                password: await hash.make('student123'),
                first_name: 'Alice',
                last_name: 'Wilson',
                user_type: 'student',
                status: 'active',
                institute_id: harvard.id,
                email_verified_at: new Date()
            },
            {
                email: '<EMAIL>',
                password: await hash.make('student123'),
                first_name: 'Charlie',
                last_name: 'Brown',
                user_type: 'student',
                status: 'active',
                institute_id: mit.id,
                email_verified_at: new Date()
            }
        ]);
    }
}
//# sourceMappingURL=002_user_seeder.js.map