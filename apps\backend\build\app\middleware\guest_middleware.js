export default class GuestMiddleware {
    async handle({ auth, response }, next) {
        await auth.check();
        if (auth.isAuthenticated) {
            return response.status(400).json({
                success: false,
                message: 'Already authenticated',
                error: 'You are already logged in'
            });
        }
        return next();
    }
}
//# sourceMappingURL=guest_middleware.js.map