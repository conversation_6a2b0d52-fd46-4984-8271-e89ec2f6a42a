import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import authService, { RegisterData, LoginData, AuthResponse, UserResponse } from '@/services/authService'

export interface User {
  id: number
  first_name: string
  last_name: string
  email: string
  user_type: string
  status: string
  is_verified: boolean
}

export interface Institute {
  id: number
  name: string
  email: string
  status: string
  is_verified: boolean
}

export interface AuthState {
  user: User | null
  institute: Institute | null
  permissions: string[]
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  tokens: {
    access_token: string | null
    refresh_token: string | null
  }
}

const initialState: AuthState = {
  user: null,
  institute: null,
  permissions: [],
  isAuthenticated: false,
  isLoading: false,
  error: null,
  tokens: {
    access_token: null,
    refresh_token: null,
  },
}

// Async thunks
export const registerInstitute = createAsyncThunk(
  'auth/register',
  async (data: RegisterData, { rejectWithValue }) => {
    try {
      const response = await authService.register(data)

      // Store tokens
      authService.setTokens(response.data.tokens)

      return response.data
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'Registration failed'
      )
    }
  }
)

export const loginInstitute = createAsyncThunk(
  'auth/login',
  async (data: LoginData, { rejectWithValue }) => {
    try {
      const response = await authService.login(data)

      // Store tokens
      authService.setTokens(response.data.tokens)

      return response.data
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'Login failed'
      )
    }
  }
)

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authService.getCurrentUser()
      return response.data
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to get user info'
      )
    }
  }
)

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authService.logout()

      // Clear tokens from storage
      authService.clearTokens()

      return response.data
    } catch (error: any) {
      // Even if API call fails, clear tokens locally
      authService.clearTokens()
      return rejectWithValue(
        error.response?.data?.message || 'Logout failed'
      )
    }
  }
)

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout()
      authService.clearTokens()
    } catch (error: any) {
      // Even if logout fails on server, clear local tokens
      authService.clearTokens()
      return rejectWithValue(
        error.response?.data?.message || 'Logout failed'
      )
    }
  }
)

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCredentials: (state, action: PayloadAction<{ user: User; institute: Institute; permissions: string[] }>) => {
      state.user = action.payload.user
      state.institute = action.payload.institute
      state.permissions = action.payload.permissions
      state.isAuthenticated = true
    },
    clearCredentials: (state) => {
      state.user = null
      state.institute = null
      state.permissions = []
      state.isAuthenticated = false
      state.tokens.access_token = null
      state.tokens.refresh_token = null
    },
    initializeAuth: (state) => {
      const accessToken = authService.getAccessToken()
      const refreshToken = authService.getRefreshToken()

      if (accessToken && refreshToken) {
        state.tokens.access_token = accessToken
        state.tokens.refresh_token = refreshToken
        state.isAuthenticated = true
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Register
      .addCase(registerInstitute.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(registerInstitute.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
        state.institute = action.payload.institute
        state.permissions = action.payload.permissions || []
        state.isAuthenticated = true
        state.tokens.access_token = action.payload.tokens.access_token
        state.tokens.refresh_token = action.payload.tokens.refresh_token
      })
      .addCase(registerInstitute.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Login
      .addCase(loginInstitute.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(loginInstitute.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
        state.institute = action.payload.institute
        state.permissions = action.payload.permissions || []
        state.isAuthenticated = true
        state.tokens.access_token = action.payload.tokens.access_token
        state.tokens.refresh_token = action.payload.tokens.refresh_token
      })
      .addCase(loginInstitute.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Get current user
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
        state.institute = action.payload.institute
        state.permissions = action.payload.permissions
        state.isAuthenticated = true
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
        state.isAuthenticated = false
      })
      // Logout
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null
        state.institute = null
        state.permissions = []
        state.isAuthenticated = false
        state.tokens.access_token = null
        state.tokens.refresh_token = null
        state.isLoading = false
        state.error = null
      })
  },
})

export const { clearError, setCredentials, clearCredentials, initializeAuth } = authSlice.actions
export default authSlice.reducer
