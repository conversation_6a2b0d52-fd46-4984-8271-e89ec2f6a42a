import mysql from 'mysql2/promise'
import { config } from 'dotenv'
import { execSync } from 'child_process'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function freshDatabase() {
  let connection

  try {
    console.log('🔄 Running fresh database setup...')
    
    // Connect to MySQL server (without specifying database)
    const serverConfig = { ...dbConfig }
    delete serverConfig.database
    
    connection = await mysql.createConnection(serverConfig)
    console.log('✅ Connected to MySQL server')

    // Drop database if exists
    console.log('🗑️  Dropping existing database...')
    await connection.execute(`DROP DATABASE IF EXISTS \`${dbConfig.database}\``)
    console.log(`✅ Database '${dbConfig.database}' dropped`)

    // Create fresh database
    console.log('🆕 Creating fresh database...')
    await connection.execute(`CREATE DATABASE \`${dbConfig.database}\``)
    console.log(`✅ Database '${dbConfig.database}' created`)

    await connection.end()

    // Run setup
    console.log('🔧 Setting up database structure...')
    execSync('node scripts/setup-database.js', { stdio: 'inherit' })

    // Run seeds
    console.log('🌱 Seeding database with sample data...')
    execSync('node scripts/seed.js', { stdio: 'inherit' })

    console.log('🎉 Fresh database setup completed successfully!')

  } catch (error) {
    console.error('❌ Fresh database setup failed:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

freshDatabase()
