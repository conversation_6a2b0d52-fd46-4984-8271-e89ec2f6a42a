import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import Role from './role.js'
import Institute from './institute.js'

export default class UserRole extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare roleId: number

  @column()
  declare instituteId: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationships
   */
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Role)
  declare role: BelongsTo<typeof Role>

  @belongsTo(() => Institute)
  declare institute: BelongsTo<typeof Institute>

  /**
   * Computed properties
   */
  get roleKey() {
    return `${this.role?.name || 'unknown'}`
  }

  get userFullName() {
    return this.user?.fullName || 'Unknown User'
  }

  get instituteName() {
    return this.institute?.name || 'Unknown Institute'
  }

  /**
   * Serialize user role data for API responses
   */
  serialize() {
    return {
      id: this.id,
      userId: this.userId,
      roleId: this.roleId,
      instituteId: this.instituteId,
      roleKey: this.roleKey,
      userFullName: this.userFullName,
      instituteName: this.instituteName,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
