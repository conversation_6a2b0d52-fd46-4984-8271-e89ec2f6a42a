{"version": 3, "file": "012_create_user_sessions_table.js", "sourceRoot": "", "sources": ["../../../database/migrations/012_create_user_sessions_table.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAA;AAEnD,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IAC3B,SAAS,GAAG,eAAe,CAAA;IAErC,KAAK,CAAC,EAAE;QACN,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAChD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAA;YAGhC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAA;YACjD,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAG9E,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAA;YACtD,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAA;YACpD,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;YAG7D,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC1C,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC3C,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC5C,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC7C,KAAK,CAAC,MAAM,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAChD,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGzC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC5C,KAAK,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC9C,KAAK,CAAC,MAAM,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAC7C,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGnC,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAA;YAC5C,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YACvC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YACtC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YACpC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YACvC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGnC,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAA;YAC1D,KAAK,CAAC,SAAS,CAAC,kBAAkB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAA;YAClE,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAA;YAC5D,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGxD,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YACxF,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACpD,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACnD,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YAC3C,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YAGxC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YAC/C,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,CAAA;YAGvC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;YAIjC,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YACpE,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YAGpE,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;YACxB,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAA;YAC3B,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAA;YACzB,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;YACvB,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAA;YAC3B,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAA;YACzB,KAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAA;YACjC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAA;YAC3B,KAAK,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAA;YACnC,KAAK,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAA;YAC5B,KAAK,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,CAAC,CAAA;YAC7B,KAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAA;YACjC,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;YAGxB,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAA;YAClC,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAA;YAC9C,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC,CAAA;YACvC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAA;YACtC,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACvC,CAAC;CACF"}