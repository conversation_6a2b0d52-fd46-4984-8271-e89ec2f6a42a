import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config()

const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT) || 3307,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'lms'
}

async function testManualSessionInsert() {
  let connection

  try {
    console.log('🔍 Testing manual session insert...')
    
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Check current session count
    const [beforeSessions] = await connection.execute('SELECT COUNT(*) as count FROM user_sessions')
    console.log(`📊 Sessions before insert: ${beforeSessions[0].count}`)

    // Try to insert a test session record
    const sessionId = `test_session_${Date.now()}`
    const tokenId = `test_token_${Date.now()}`
    const loginTime = new Date()
    const expiresAt = new Date(loginTime.getTime() + 24 * 60 * 60 * 1000)

    console.log('🔄 Attempting to insert test session...')
    
    try {
      const result = await connection.execute(`
        INSERT INTO user_sessions (
          user_id, session_id, token_id, token_type,
          device_type, browser_name, browser_version, operating_system,
          user_agent, ip_address, last_activity_at, expires_at,
          status, is_current_session, is_mobile, is_bot
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        2, // user_id (John Doe from seed data)
        sessionId,
        tokenId,
        'access',
        'desktop',
        'Chrome',
        '120.0.0.0',
        'Windows',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        '127.0.0.1',
        loginTime,
        expiresAt,
        'active',
        1, // is_current_session
        0, // is_mobile
        0  // is_bot
      ])
      
      console.log('✅ Session insert successful:', result[0])
      
    } catch (insertError) {
      console.error('❌ Session insert failed:', insertError.message)
      console.error('❌ Full error:', insertError)
    }

    // Check session count after insert
    const [afterSessions] = await connection.execute('SELECT COUNT(*) as count FROM user_sessions')
    console.log(`📊 Sessions after insert: ${afterSessions[0].count}`)

    // Get the latest session details
    const [latestSession] = await connection.execute(`
      SELECT 
        user_id, session_id, token_id, device_type, browser_name, 
        operating_system, ip_address, status, is_mobile, login_at
      FROM user_sessions 
      ORDER BY created_at DESC 
      LIMIT 1
    `)

    if (latestSession.length > 0) {
      const session = latestSession[0]
      console.log('\n🎯 Latest session details:')
      console.log(`   User ID: ${session.user_id}`)
      console.log(`   Session ID: ${session.session_id}`)
      console.log(`   Token ID: ${session.token_id}`)
      console.log(`   Device Type: ${session.device_type}`)
      console.log(`   Browser: ${session.browser_name}`)
      console.log(`   OS: ${session.operating_system}`)
      console.log(`   IP Address: ${session.ip_address}`)
      console.log(`   Status: ${session.status}`)
      console.log(`   Is Mobile: ${session.is_mobile}`)
      console.log(`   Login Time: ${session.login_at}`)
    }

    console.log('\n🎉 Manual session insert test completed!')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

testManualSessionInsert()
