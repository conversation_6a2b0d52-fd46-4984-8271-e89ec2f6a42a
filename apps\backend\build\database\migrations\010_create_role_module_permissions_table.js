import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'role_module_permissions';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.integer('role_id').unsigned().notNullable();
            table.integer('module_id').unsigned().notNullable();
            table.integer('permission_id').unsigned().notNullable();
            table.integer('institute_id').unsigned().notNullable();
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.unique(['role_id', 'module_id', 'permission_id', 'institute_id'], 'unique_role_module_perm_inst');
            table.foreign('role_id').references('id').inTable('roles').onDelete('CASCADE');
            table.foreign('module_id').references('id').inTable('modules').onDelete('CASCADE');
            table.foreign('permission_id').references('id').inTable('permissions').onDelete('CASCADE');
            table.foreign('institute_id').references('id').inTable('institutes').onDelete('CASCADE');
            table.index(['role_id']);
            table.index(['module_id']);
            table.index(['permission_id']);
            table.index(['institute_id']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=010_create_role_module_permissions_table.js.map