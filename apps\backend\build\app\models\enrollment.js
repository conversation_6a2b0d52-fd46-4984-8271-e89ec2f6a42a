var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm';
import User from './user.js';
import Course from './course.js';
export default class Enrollment extends BaseModel {
    get isCompleted() {
        return this.status === 'completed';
    }
    get isActive() {
        return this.status === 'enrolled';
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], Enrollment.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Enrollment.prototype, "userId", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Enrollment.prototype, "courseId", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], Enrollment.prototype, "status", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], Enrollment.prototype, "progress", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Enrollment.prototype, "grade", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], Enrollment.prototype, "completedAt", void 0);
__decorate([
    column(),
    __metadata("design:type", DateTime)
], Enrollment.prototype, "enrolledAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], Enrollment.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], Enrollment.prototype, "updatedAt", void 0);
__decorate([
    belongsTo(() => User),
    __metadata("design:type", Object)
], Enrollment.prototype, "user", void 0);
__decorate([
    belongsTo(() => Course),
    __metadata("design:type", Object)
], Enrollment.prototype, "course", void 0);
//# sourceMappingURL=enrollment.js.map