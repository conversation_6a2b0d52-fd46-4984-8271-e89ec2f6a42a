# Phase 1: Authentication Module Institute(Login, Register)

## ✅ Backend Goals

- [ ] Create `POST /register` endpoint
- [ ] Create `POST /login` endpoint
- [ ] Create `GET /me` for user profile
- [ ] Integrate Swagger docs for each API

## ✅ Frontend Goals

- [ ] Login form UI
- [ ] Register form UI
- [ ] Call API from frontend using Axios
- [ ] Store token (localStorage or cookie)
- [ ] Redirect after login

## 📦 Tech

- Backend: Node.js + AdonisJS (or Express) + Swagger
- Frontend: Next.js + Tailwind + Axios + Redux Toolkit

## 📌 Swagger Docs Path

- Swagger URL: `/docs`
- Format: OpenAPI 3.0
