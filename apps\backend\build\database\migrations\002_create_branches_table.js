import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'branches';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.integer('institute_id').unsigned().notNullable();
            table.foreign('institute_id').references('id').inTable('institutes').onDelete('CASCADE');
            table.string('name', 255).notNullable();
            table.string('code', 50).notNullable();
            table.text('description').nullable();
            table.text('address').nullable();
            table.string('city', 100).nullable();
            table.string('state', 100).nullable();
            table.string('country', 100).nullable();
            table.string('zip_code', 20).nullable();
            table.string('phone', 20).nullable();
            table.string('email', 255).nullable();
            table.enum('status', ['active', 'inactive']).defaultTo('active');
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.index(['institute_id']);
            table.index(['status']);
            table.unique(['institute_id', 'code']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=002_create_branches_table.js.map