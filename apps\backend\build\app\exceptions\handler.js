import app from '@adonisjs/core/services/app';
import { ExceptionHandler } from '@adonisjs/core/http';
import { errors as vineErrors } from '@vinejs/vine';
export default class HttpExceptionHandler extends ExceptionHandler {
    debug = !app.inProduction;
    async handle(error, ctx) {
        const { response, logger } = ctx;
        if (error instanceof vineErrors.E_VALIDATION_ERROR) {
            return response.status(422).json({
                success: false,
                message: 'Validation failed',
                errors: error.messages
            });
        }
        if (error instanceof Error && error.message.includes('E_UNAUTHORIZED_ACCESS')) {
            return response.status(401).json({
                success: false,
                message: 'Unauthorized access',
                error: 'Authentication required'
            });
        }
        if (error instanceof Error && error.message.includes('E_ROUTE_NOT_FOUND')) {
            return response.status(404).json({
                success: false,
                message: 'Route not found',
                error: 'The requested endpoint does not exist'
            });
        }
        if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
            logger.error('Database connection failed', { error: error.message });
            return response.status(503).json({
                success: false,
                message: 'Service unavailable',
                error: 'Database connection failed'
            });
        }
        if (error instanceof Error && (error.message.includes('jwt') ||
            error.message.includes('token'))) {
            return response.status(401).json({
                success: false,
                message: 'Invalid token',
                error: 'Authentication token is invalid or expired'
            });
        }
        logger.error('Unhandled exception', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            url: ctx.request.url(),
            method: ctx.request.method(),
            ip: ctx.request.ip()
        });
        if (this.debug) {
            return super.handle(error, ctx);
        }
        return response.status(500).json({
            success: false,
            message: 'Internal server error',
            error: 'Something went wrong on our end'
        });
    }
    async report(error, ctx) {
        return super.report(error, ctx);
    }
}
//# sourceMappingURL=handler.js.map