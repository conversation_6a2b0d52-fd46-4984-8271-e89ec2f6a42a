{"version": 3, "file": "005_permission_seeder.js", "sourceRoot": "", "sources": ["../../../database/seeders/005_permission_seeder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,UAAU,MAAM,oBAAoB,CAAA;AAC3C,OAAO,MAAM,MAAM,gBAAgB,CAAA;AAEnC,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IACrC,KAAK,CAAC,GAAG;QAEP,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAC9D,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QAClE,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QACpE,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;QAC1E,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QAClE,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QAClE,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QAGpE,MAAM,UAAU,CAAC,UAAU,CAAC;YAC1B;gBACE,IAAI,EAAE,YAAY;gBAClB,YAAY,EAAE,YAAY;gBAC1B,WAAW,EAAE,gCAAgC;gBAC7C,SAAS,EAAE,WAAW,CAAC,EAAE;aAC1B;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,YAAY,EAAE,cAAc;gBAC5B,WAAW,EAAE,sBAAsB;gBACnC,SAAS,EAAE,WAAW,CAAC,EAAE;aAC1B;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,YAAY,EAAE,YAAY;gBAC1B,WAAW,EAAE,yBAAyB;gBACtC,SAAS,EAAE,WAAW,CAAC,EAAE;aAC1B;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,YAAY,EAAE,cAAc;gBAC5B,WAAW,EAAE,kBAAkB;gBAC/B,SAAS,EAAE,WAAW,CAAC,EAAE;aAC1B;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,YAAY,EAAE,YAAY;gBAC1B,WAAW,EAAE,gCAAgC;gBAC7C,SAAS,EAAE,WAAW,CAAC,EAAE;aAC1B;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,YAAY,EAAE,cAAc;gBAC5B,WAAW,EAAE,2BAA2B;gBACxC,SAAS,EAAE,WAAW,CAAC,EAAE;aAC1B;SACF,CAAC,CAAA;QAGF,MAAM,UAAU,CAAC,UAAU,CAAC;YAC1B;gBACE,IAAI,EAAE,cAAc;gBACpB,YAAY,EAAE,cAAc;gBAC5B,WAAW,EAAE,kCAAkC;gBAC/C,SAAS,EAAE,aAAa,CAAC,EAAE;aAC5B;YACD;gBACE,IAAI,EAAE,gBAAgB;gBACtB,YAAY,EAAE,gBAAgB;gBAC9B,WAAW,EAAE,wBAAwB;gBACrC,SAAS,EAAE,aAAa,CAAC,EAAE;aAC5B;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,YAAY,EAAE,cAAc;gBAC5B,WAAW,EAAE,2BAA2B;gBACxC,SAAS,EAAE,aAAa,CAAC,EAAE;aAC5B;YACD;gBACE,IAAI,EAAE,gBAAgB;gBACtB,YAAY,EAAE,gBAAgB;gBAC9B,WAAW,EAAE,oBAAoB;gBACjC,SAAS,EAAE,aAAa,CAAC,EAAE;aAC5B;SACF,CAAC,CAAA;QAGF,MAAM,UAAU,CAAC,UAAU,CAAC;YAC1B;gBACE,IAAI,EAAE,eAAe;gBACrB,YAAY,EAAE,eAAe;gBAC7B,WAAW,EAAE,mCAAmC;gBAChD,SAAS,EAAE,cAAc,CAAC,EAAE;aAC7B;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,YAAY,EAAE,iBAAiB;gBAC/B,WAAW,EAAE,iCAAiC;gBAC9C,SAAS,EAAE,cAAc,CAAC,EAAE;aAC7B;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,YAAY,EAAE,eAAe;gBAC7B,WAAW,EAAE,8BAA8B;gBAC3C,SAAS,EAAE,cAAc,CAAC,EAAE;aAC7B;YACD;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,YAAY,EAAE,oBAAoB;gBAClC,WAAW,EAAE,gCAAgC;gBAC7C,SAAS,EAAE,cAAc,CAAC,EAAE;aAC7B;SACF,CAAC,CAAA;IAGJ,CAAC;CACF"}