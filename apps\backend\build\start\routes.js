import router from '@adonisjs/core/services/router';
import { middleware } from './kernel.js';
import swaggerUi from 'swagger-ui-express';
import { swaggerSpec } from '#config/swagger';
router.get('/health', async () => {
    return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'LMS Platform API',
        version: '1.0.0'
    };
});
router.get('/docs', async ({ response }) => {
    const html = swaggerUi.generateHTML(swaggerSpec, {
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'LMS Platform API Documentation'
    });
    response.header('Content-Type', 'text/html');
    return html;
});
router.get('/docs/swagger.json', async () => {
    return swaggerSpec;
});
router.group(() => {
    router.group(() => {
        router.post('/login', '#controllers/auth/super_admin_auth_controller.login');
        router.post('/refresh', '#controllers/auth/super_admin_auth_controller.refresh');
        router.group(() => {
            router.get('/me', '#controllers/auth/super_admin_auth_controller.me');
            router.get('/dashboard', '#controllers/auth/super_admin_auth_controller.dashboard');
            router.post('/logout', '#controllers/auth/super_admin_auth_controller.logout');
        }).use(middleware.auth({ guards: ['jwt'] }));
    }).prefix('/auth/super-admin');
    router.group(() => {
        router.post('/register', '#controllers/auth/institute_auth_controller.register');
        router.post('/login', '#controllers/auth/institute_auth_controller.login');
        router.post('/refresh', '#controllers/auth/institute_auth_controller.refresh');
        router.group(() => {
            router.get('/me', '#controllers/auth/institute_auth_controller.me');
            router.get('/dashboard', '#controllers/auth/institute_auth_controller.dashboard');
            router.post('/logout', '#controllers/auth/institute_auth_controller.logout');
        }).use(middleware.auth({ guards: ['jwt'] }));
    }).prefix('/auth/institute');
    router.group(() => {
        router.post('/login', '#controllers/auth/student_auth_controller.login');
        router.post('/refresh', '#controllers/auth/student_auth_controller.refresh');
        router.group(() => {
            router.get('/me', '#controllers/auth/student_auth_controller.me');
            router.get('/dashboard', '#controllers/auth/student_auth_controller.dashboard');
            router.post('/logout', '#controllers/auth/student_auth_controller.logout');
        }).use(middleware.auth({ guards: ['jwt'] }));
    }).prefix('/auth/student');
    router.group(() => {
        router.group(() => {
            router.get('/', '#controllers/super_admin/institutes_controller.index');
            router.post('/', '#controllers/super_admin/institutes_controller.store');
            router.get('/:id', '#controllers/super_admin/institutes_controller.show');
            router.put('/:id', '#controllers/super_admin/institutes_controller.update');
            router.delete('/:id', '#controllers/super_admin/institutes_controller.destroy');
            router.post('/:id/suspend', '#controllers/super_admin/institutes_controller.suspend');
            router.post('/:id/activate', '#controllers/super_admin/institutes_controller.activate');
        }).prefix('/institutes');
        router.group(() => {
            router.get('/', '#controllers/super_admin/users_controller.index');
            router.post('/', '#controllers/super_admin/users_controller.store');
            router.get('/:id', '#controllers/super_admin/users_controller.show');
            router.put('/:id', '#controllers/super_admin/users_controller.update');
            router.delete('/:id', '#controllers/super_admin/users_controller.destroy');
        }).prefix('/users');
        router.group(() => {
            router.get('/overview', '#controllers/super_admin/analytics_controller.overview');
            router.get('/institutes', '#controllers/super_admin/analytics_controller.institutes');
            router.get('/users', '#controllers/super_admin/analytics_controller.users');
            router.get('/revenue', '#controllers/super_admin/analytics_controller.revenue');
        }).prefix('/analytics');
    }).prefix('/super-admin').use(middleware.auth({ guards: ['jwt'] }));
    router.group(() => {
        router.group(() => {
            router.get('/', '#controllers/institute_admin/students_controller.index');
            router.post('/', '#controllers/institute_admin/students_controller.store');
            router.get('/:id', '#controllers/institute_admin/students_controller.show');
            router.put('/:id', '#controllers/institute_admin/students_controller.update');
            router.delete('/:id', '#controllers/institute_admin/students_controller.destroy');
        }).prefix('/students');
        router.group(() => {
            router.get('/', '#controllers/institute_admin/courses_controller.index');
            router.post('/', '#controllers/institute_admin/courses_controller.store');
            router.get('/:id', '#controllers/institute_admin/courses_controller.show');
            router.put('/:id', '#controllers/institute_admin/courses_controller.update');
            router.delete('/:id', '#controllers/institute_admin/courses_controller.destroy');
        }).prefix('/courses');
        router.group(() => {
            router.get('/', '#controllers/institute_admin/enrollments_controller.index');
            router.post('/', '#controllers/institute_admin/enrollments_controller.store');
            router.get('/:id', '#controllers/institute_admin/enrollments_controller.show');
            router.put('/:id', '#controllers/institute_admin/enrollments_controller.update');
            router.delete('/:id', '#controllers/institute_admin/enrollments_controller.destroy');
        }).prefix('/enrollments');
        router.group(() => {
            router.get('/overview', '#controllers/institute_admin/analytics_controller.overview');
            router.get('/students', '#controllers/institute_admin/analytics_controller.students');
            router.get('/courses', '#controllers/institute_admin/analytics_controller.courses');
        }).prefix('/analytics');
    }).prefix('/institute-admin').use(middleware.auth({ guards: ['jwt'] }));
    router.group(() => {
        router.group(() => {
            router.get('/', '#controllers/student/courses_controller.index');
            router.get('/:id', '#controllers/student/courses_controller.show');
            router.get('/:id/lessons', '#controllers/student/courses_controller.lessons');
            router.post('/:id/enroll', '#controllers/student/courses_controller.enroll');
        }).prefix('/courses');
        router.group(() => {
            router.get('/', '#controllers/student/assignments_controller.index');
            router.get('/:id', '#controllers/student/assignments_controller.show');
            router.post('/:id/submit', '#controllers/student/assignments_controller.submit');
        }).prefix('/assignments');
        router.group(() => {
            router.get('/', '#controllers/student/grades_controller.index');
            router.get('/transcript', '#controllers/student/grades_controller.transcript');
        }).prefix('/grades');
        router.group(() => {
            router.get('/', '#controllers/student/profile_controller.show');
            router.put('/', '#controllers/student/profile_controller.update');
            router.post('/avatar', '#controllers/student/profile_controller.uploadAvatar');
        }).prefix('/profile');
    }).prefix('/student').use(middleware.auth({ guards: ['jwt'] }));
    router.group(() => {
        router.get('/', '#controllers/user_sessions_controller.index');
        router.get('/analytics', '#controllers/user_sessions_controller.analytics');
        router.get('/security-alerts', '#controllers/user_sessions_controller.securityAlerts');
        router.get('/:id', '#controllers/user_sessions_controller.show');
        router.delete('/:id/revoke', '#controllers/user_sessions_controller.revoke');
        router.delete('/revoke-all', '#controllers/user_sessions_controller.revokeAll');
        router.post('/:id/trust', '#controllers/user_sessions_controller.trustDevice');
    }).prefix('/sessions').use(middleware.auth({ guards: ['jwt'] }));
}).prefix('/api/v1');
router.any('*', async ({ response }) => {
    return response.status(404).json({
        success: false,
        message: 'Endpoint not found',
        error: 'The requested resource does not exist'
    });
});
//# sourceMappingURL=routes.js.map