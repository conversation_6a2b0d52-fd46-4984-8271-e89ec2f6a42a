import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'courses';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.integer('institute_id').unsigned().notNullable();
            table.foreign('institute_id').references('id').inTable('institutes').onDelete('CASCADE');
            table.integer('instructor_id').unsigned().notNullable();
            table.foreign('instructor_id').references('id').inTable('users').onDelete('CASCADE');
            table.string('title', 255).notNullable();
            table.string('slug', 255).notNullable();
            table.text('description').nullable();
            table.text('short_description').nullable();
            table.string('thumbnail', 500).nullable();
            table.decimal('price', 10, 2).defaultTo(0);
            table.decimal('discount_price', 10, 2).nullable();
            table.string('currency', 3).defaultTo('USD');
            table.integer('duration').nullable();
            table.enum('level', ['beginner', 'intermediate', 'advanced']).defaultTo('beginner');
            table.string('category', 100).nullable();
            table.json('tags').nullable();
            table.enum('status', ['draft', 'published', 'archived']).defaultTo('draft');
            table.integer('max_students').nullable();
            table.timestamp('start_date', { useTz: true }).nullable();
            table.timestamp('end_date', { useTz: true }).nullable();
            table.json('requirements').nullable();
            table.json('learning_outcomes').nullable();
            table.boolean('is_featured').defaultTo(false);
            table.boolean('is_public').defaultTo(true);
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.index(['institute_id']);
            table.index(['instructor_id']);
            table.index(['status']);
            table.index(['level']);
            table.index(['category']);
            table.index(['is_featured']);
            table.index(['created_at']);
            table.unique(['institute_id', 'slug']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=004_create_courses_table.js.map