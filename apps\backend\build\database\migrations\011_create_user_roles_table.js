import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'user_roles';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.integer('user_id').unsigned().notNullable();
            table.integer('role_id').unsigned().notNullable();
            table.integer('institute_id').unsigned().notNullable();
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.unique(['user_id', 'role_id', 'institute_id']);
            table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
            table.foreign('role_id').references('id').inTable('roles').onDelete('CASCADE');
            table.foreign('institute_id').references('id').inTable('institutes').onDelete('CASCADE');
            table.index(['user_id']);
            table.index(['role_id']);
            table.index(['institute_id']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=011_create_user_roles_table.js.map