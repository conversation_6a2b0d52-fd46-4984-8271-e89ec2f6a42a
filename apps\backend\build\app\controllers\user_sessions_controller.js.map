{"version": 3, "file": "user_sessions_controller.js", "sourceRoot": "", "sources": ["../../../app/controllers/user_sessions_controller.ts"], "names": [], "mappings": "AACA,OAAO,WAAW,MAAM,sBAAsB,CAAA;AAC9C,OAAO,cAAc,MAAM,2BAA2B,CAAA;AAEtD,MAAM,CAAC,OAAO,OAAO,sBAAsB;IACjC,cAAc,GAAG,IAAI,cAAc,EAAE,CAAA;IAK7C,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAe;QACzC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAEzE,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;oBAC7D,KAAK,EAAE,QAAQ,CAAC,MAAM;iBACvB;aACF,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;gBACnC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAe;QAChD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;YACvB,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;iBACtC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;iBACtB,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;iBACxB,KAAK,EAAE,CAAA;YAEV,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC/B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,mBAAmB;iBAC7B,CAAC,CAAA;YACJ,CAAC;YAED,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,OAAO,CAAC,SAAS,EAAE;aAC1B,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,iCAAiC;gBAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAe;QAClD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;YACvB,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;iBACtC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;iBACtB,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;iBACxB,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;iBACzB,KAAK,EAAE,CAAA;YAEV,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC/B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,0BAA0B;iBACpC,CAAC,CAAA;YACJ,CAAC;YAED,MAAM,OAAO,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAA;YAEvD,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,8BAA8B;aACxC,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;gBACnC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAe;QAC7C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;YAGvB,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAEvD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAClE,IAAI,CAAC,EAAE,EACP,gBAAgB,CACjB,CAAA;YAED,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,GAAG,YAAY,gCAAgC;gBACxD,IAAI,EAAE;oBACJ,YAAY;iBACb;aACF,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2BAA2B;gBACpC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAe;QACvD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;YACvB,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;iBACtC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;iBACtB,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;iBACxB,KAAK,EAAE,CAAA;YAEV,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC/B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,mBAAmB;iBAC7B,CAAC,CAAA;YACJ,CAAC;YAED,MAAM,OAAO,CAAC,aAAa,EAAE,CAAA;YAE7B,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,0BAA0B;aACpC,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,wBAAwB;gBACjC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAe;QAC7C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;YAGvB,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;iBACvC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;iBACxB,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;iBACtE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;YAG7B,MAAM,SAAS,GAAG;gBAChB,aAAa,EAAE,QAAQ,CAAC,MAAM;gBAC9B,cAAc,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM;gBAClE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC;gBACjD,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,aAAa,CAAC;gBAC/C,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,iBAAiB,CAAC;gBAC3D,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC;gBAC5C,kBAAkB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,MAAM;gBAC/D,sBAAsB,EAAE,IAAI,CAAC,+BAA+B,CAAC,QAAQ,CAAC;gBACtE,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;aACjD,CAAA;YAED,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,SAAS;aAChB,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mCAAmC;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAe;QAClD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;YAEvB,MAAM,kBAAkB,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE;iBACjD,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;iBACxB,KAAK,CAAC,cAAc,EAAE,IAAI,CAAC;iBAC3B,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;iBACrE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;YAE7B,MAAM,MAAM,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAChD,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,IAAI,EAAE,kBAAkB;gBACxB,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,OAAO,CAAC,aAAa,IAAI,2BAA2B;gBAC7D,SAAS,EAAE,OAAO,CAAC,OAAO;gBAC1B,OAAO,EAAE;oBACP,QAAQ,EAAE,GAAG,OAAO,CAAC,IAAI,IAAI,SAAS,KAAK,OAAO,CAAC,OAAO,IAAI,SAAS,EAAE;oBACzE,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,MAAM,OAAO,CAAC,eAAe,EAAE;oBAC5D,OAAO,EAAE,OAAO,CAAC,WAAW;oBAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;iBAC7B;aACF,CAAC,CAAC,CAAA;YAEH,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,MAAM;oBACN,KAAK,EAAE,MAAM,CAAC,MAAM;iBACrB;aACF,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,iCAAiC;gBAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKO,mBAAmB,CAAC,IAAS;QAGnC,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAA;IACpC,CAAC;IAKO,OAAO,CAAC,KAAY,EAAE,QAAgB;QAC5C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAA;YACvC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAC9B,OAAO,GAAG,CAAA;QACZ,CAAC,EAAE,EAAE,CAAC,CAAA;IACR,CAAC;IAKO,+BAA+B,CAAC,QAAuB;QAC7D,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;QAC1D,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAA;QAE5C,MAAM,aAAa,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YAC9D,OAAO,GAAG,GAAG,OAAO,CAAC,eAAe,CAAA;QACtC,CAAC,EAAE,CAAC,CAAC,CAAA;QAEL,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAA;IAC7D,CAAC;IAKO,oBAAoB,CAAC,QAAuB;QAClD,MAAM,MAAM,GAA2B,EAAE,CAAA;QAEzC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;YACnD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACxC,CAAC,CAAC,CAAA;QAEF,OAAO,MAAM,CAAA;IACf,CAAC;CACF"}