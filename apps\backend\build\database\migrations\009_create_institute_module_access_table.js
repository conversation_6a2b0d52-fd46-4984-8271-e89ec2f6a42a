import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'institute_module_access';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.integer('institute_id').unsigned().notNullable();
            table.integer('module_id').unsigned().notNullable();
            table.boolean('is_active').defaultTo(true);
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.unique(['institute_id', 'module_id']);
            table.foreign('institute_id').references('id').inTable('institutes').onDelete('CASCADE');
            table.foreign('module_id').references('id').inTable('modules').onDelete('CASCADE');
            table.index(['institute_id']);
            table.index(['module_id']);
            table.index(['is_active']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=009_create_institute_module_access_table.js.map