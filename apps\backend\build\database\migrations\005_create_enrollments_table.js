import { BaseSchema } from '@adonisjs/lucid/schema';
export default class extends BaseSchema {
    tableName = 'enrollments';
    async up() {
        this.schema.createTable(this.tableName, (table) => {
            table.increments('id').primary();
            table.integer('user_id').unsigned().notNullable();
            table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
            table.integer('course_id').unsigned().notNullable();
            table.foreign('course_id').references('id').inTable('courses').onDelete('CASCADE');
            table.enum('status', ['enrolled', 'completed', 'dropped', 'suspended']).defaultTo('enrolled');
            table.decimal('progress', 5, 2).defaultTo(0);
            table.string('grade', 10).nullable();
            table.timestamp('completed_at', { useTz: true }).nullable();
            table.timestamp('enrolled_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('created_at', { useTz: true }).defaultTo(this.now());
            table.timestamp('updated_at', { useTz: true }).defaultTo(this.now());
            table.index(['user_id']);
            table.index(['course_id']);
            table.index(['status']);
            table.index(['enrolled_at']);
            table.unique(['user_id', 'course_id']);
        });
    }
    async down() {
        this.schema.dropTable(this.tableName);
    }
}
//# sourceMappingURL=005_create_enrollments_table.js.map