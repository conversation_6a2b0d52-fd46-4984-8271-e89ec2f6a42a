import User from '#models/user';
import Institute from '#models/institute';
import { instituteRegisterValidator, instituteLoginValidator, refreshTokenValidator } from '#validators/auth_validator';
import InstituteAuthService from '#services/auth/institute_auth_service';
export default class InstituteAuthController {
    authService = new InstituteAuthService();
    async register({ request, response }) {
        try {
            const payload = await request.validateUsing(instituteRegisterValidator);
            const existingUser = await User.findBy('email', payload.email);
            if (existingUser) {
                return response.status(400).json({
                    success: false,
                    message: 'Email already exists',
                    errors: {
                        email: ['This email is already registered']
                    }
                });
            }
            const existingInstitute = await Institute.findBy('email', payload.email);
            if (existingInstitute) {
                return response.status(400).json({
                    success: false,
                    message: 'Institute with this email already exists',
                    errors: {
                        email: ['An institute with this email already exists']
                    }
                });
            }
            const slug = payload.instituteName
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            const institute = await Institute.create({
                name: payload.instituteName,
                code: slug.toUpperCase(),
                slug: slug,
                email: payload.email,
                status: 'pending',
                subscriptionPlan: 'basic',
                subscriptionStatus: 'trial',
                timezone: 'UTC',
                currency: 'USD',
                language: 'en',
                maxStudents: 100,
                maxStaff: 10,
                maxCourses: 50
            });
            const user = await User.create({
                instituteId: institute.id,
                firstName: payload.firstName,
                lastName: payload.lastName,
                email: payload.email,
                password: payload.password,
                role: 'institute_admin',
                status: 'pending',
                emailVerifiedAt: null
            });
            const tokens = await this.authService.generateTokens(user);
            return response.status(201).json({
                success: true,
                message: 'Institute registered successfully. Please wait for approval.',
                data: {
                    user: user.serialize(),
                    institute: institute.serialize(),
                    tokens
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async login({ request, response }) {
        try {
            const payload = await request.validateUsing(instituteLoginValidator);
            const user = await User.query()
                .where('email', payload.email)
                .whereIn('role', ['institute_admin', 'institute_staff'])
                .preload('institute')
                .first();
            if (!user) {
                return response.status(401).json({
                    success: false,
                    message: 'Invalid credentials',
                    errors: {
                        email: ['Invalid email or password']
                    }
                });
            }
            if (user.institute && user.institute.status !== 'active') {
                return response.status(403).json({
                    success: false,
                    message: 'Institute is not active',
                    errors: {
                        institute: ['Institute has been suspended or deactivated']
                    }
                });
            }
            const isPasswordValid = await user.verifyPassword(payload.password);
            if (!isPasswordValid) {
                return response.status(401).json({
                    success: false,
                    message: 'Invalid credentials',
                    errors: {
                        password: ['Invalid email or password']
                    }
                });
            }
            if (user.status !== 'active') {
                return response.status(403).json({
                    success: false,
                    message: 'Account is not active',
                    errors: {
                        status: ['Your account has been suspended or deactivated']
                    }
                });
            }
            await user.load('branch');
            const tokens = await this.authService.generateTokens(user);
            await this.authService.updateLastLogin(user, request.ip());
            return response.status(200).json({
                success: true,
                message: 'Login successful',
                data: {
                    user: user.serialize(),
                    institute: user.institute?.serialize(),
                    tokens,
                    permissions: await this.authService.getUserPermissions(user)
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async me({ auth, response }) {
        try {
            const user = auth.user;
            await user.load('institute');
            await user.load('branch');
            return response.status(200).json({
                success: true,
                data: {
                    user: user.serialize(),
                    institute: user.institute?.serialize(),
                    permissions: await this.authService.getUserPermissions(user)
                }
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async dashboard({ auth, response }) {
        try {
            const user = auth.user;
            const dashboardData = await this.authService.getDashboardData(user);
            return response.status(200).json({
                success: true,
                data: dashboardData
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
    async refresh({ request, response }) {
        try {
            const payload = await request.validateUsing(refreshTokenValidator);
            const tokens = await this.authService.refreshTokens(payload.refreshToken);
            return response.status(200).json({
                success: true,
                message: 'Token refreshed successfully',
                data: { tokens }
            });
        }
        catch (error) {
            return response.status(401).json({
                success: false,
                message: 'Invalid refresh token',
                error: error.message
            });
        }
    }
    async logout({ auth, response }) {
        try {
            const user = auth.user;
            await this.authService.logout(user);
            return response.status(200).json({
                success: true,
                message: 'Logged out successfully'
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
}
//# sourceMappingURL=institute_auth_controller.js.map