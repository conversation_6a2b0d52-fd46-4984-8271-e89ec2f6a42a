import { BaseSeeder } from '@adonisjs/lucid/seeders';
import Module from '#models/module';
export default class extends BaseSeeder {
    async run() {
        await Module.createMany([
            {
                name: 'users',
                display_name: 'User Management',
                description: 'Manage users, roles and permissions',
                is_active: true
            },
            {
                name: 'courses',
                display_name: 'Course Management',
                description: 'Manage courses, lessons and materials',
                is_active: true
            },
            {
                name: 'students',
                display_name: 'Student Management',
                description: 'Manage students, enrollments and progress',
                is_active: true
            },
            {
                name: 'instructors',
                display_name: 'Instructor Management',
                description: 'Manage instructors and assignments',
                is_active: true
            },
            {
                name: 'finance',
                display_name: 'Finance Management',
                description: 'Manage payments, invoices and financial reports',
                is_active: true
            },
            {
                name: 'reports',
                display_name: 'Reports',
                description: 'Access and generate various reports',
                is_active: true
            },
            {
                name: 'settings',
                display_name: 'Settings',
                description: 'Configure system settings',
                is_active: true
            }
        ]);
    }
}
//# sourceMappingURL=003_module_seeder.js.map