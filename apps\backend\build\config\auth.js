import { defineConfig } from '@adonisjs/auth';
import { sessionGuard, sessionUserProvider } from '@adonisjs/auth/session';
import { jwtGuard, jwtUserProvider } from '@adonisjs/auth/jwt';
const authConfig = defineConfig({
    default: 'jwt',
    guards: {
        web: sessionGuard({
            useRememberMeTokens: false,
            provider: sessionUserProvider({
                model: () => import('#models/user'),
            }),
        }),
        jwt: jwtGuard({
            tokenExpiresIn: '24h',
            refreshTokenExpiresIn: '7d',
            useCookies: false,
            provider: jwtUserProvider({
                model: () => import('#models/user'),
            }),
        }),
        api: jwtGuard({
            tokenExpiresIn: '1h',
            refreshTokenExpiresIn: '24h',
            useCookies: false,
            provider: jwtUserProvider({
                model: () => import('#models/user'),
            }),
        }),
    },
});
export default authConfig;
//# sourceMappingURL=auth.js.map