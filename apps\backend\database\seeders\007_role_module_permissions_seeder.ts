import { BaseSeeder } from '@adonisjs/lucid/seeders'
import RoleModulePermission from '#models/role_module_permission'
import Role from '#models/role'
import Module from '#models/module'
import Permission from '#models/permission'
import Institute from '#models/institute'

export default class extends BaseSeeder {
  async run() {
    // Get institutes
    const demoInstitute = await Institute.findByOrFail('slug', 'demo-institute')
    const harvard = await Institute.findByOrFail('slug', 'harvard-university')
    const mit = await Institute.findByOrFail('slug', 'mit')
    
    // Get roles
    const instituteAdminRole = await Role.findByOrFail('name', 'institute_admin')
    const instituteStaffRole = await Role.findByOrFail('name', 'institute_staff')
    const instructorRole = await Role.findByOrFail('name', 'instructor')
    
    // Assign full permissions to institute admin for demo institute
    await this.assignFullPermissionsToRole(instituteAdminRole.id, demoInstitute.id)
    
    // Assign limited permissions to institute staff for demo institute
    await this.assignLimitedPermissionsToRole(instituteStaffRole.id, demoInstitute.id)
    
    // Assign instructor permissions for demo institute
    await this.assignInstructorPermissions(instructorRole.id, demoInstitute.id)
    
    // Repeat for Harvard and MIT
    await this.assignFullPermissionsToRole(instituteAdminRole.id, harvard.id)
    await this.assignLimitedPermissionsToRole(instituteStaffRole.id, harvard.id)
    await this.assignInstructorPermissions(instructorRole.id, harvard.id)
    
    await this.assignFullPermissionsToRole(instituteAdminRole.id, mit.id)
    await this.assignLimitedPermissionsToRole(instituteStaffRole.id, mit.id)
    await this.assignInstructorPermissions(instructorRole.id, mit.id)
  }
  
  // Helper method to assign full permissions to a role
  async assignFullPermissionsToRole(roleId, instituteId) {
    // Get all modules and their permissions
    const modules = await Module.query().preload('permissions')
    
    for (const module of modules) {
      for (const permission of module.permissions) {
        await RoleModulePermission.create({
          role_id: roleId,
          module_id: module.id,
          permission_id: permission.id,
          institute_id: instituteId
        })
      }
    }
  }
  
  // Helper method to assign limited permissions to a role
  async assignLimitedPermissionsToRole(roleId, instituteId) {
    // Get modules
    const usersModule = await Module.findByOrFail('name', 'users')
    const coursesModule = await Module.findByOrFail('name', 'courses')
    const studentsModule = await Module.findByOrFail('name', 'students')
    
    // Get view permissions
    const viewUsersPermission = await Permission.findByOrFail('name', 'users.view')
    const viewCoursesPermission = await Permission.findByOrFail('name', 'courses.view')
    const viewStudentsPermission = await Permission.findByOrFail('name', 'students.view')
    
    // Assign view permissions
    await RoleModulePermission.createMany([
      {
        role_id: roleId,
        module_id: usersModule.id,
        permission_id: viewUsersPermission.id,
        institute_id: instituteId
      },
      {
        role_id: roleId,
        module_id: coursesModule.id,
        permission_id: viewCoursesPermission.id,
        institute_id: instituteId
      },
      {
        role_id: roleId,
        module_id: studentsModule.id,
        permission_id: viewStudentsPermission.id,
        institute_id: instituteId
      }
    ])
  }
  
  // Helper method to assign instructor permissions
  async assignInstructorPermissions(roleId, instituteId) {
    // Get modules
    const coursesModule = await Module.findByOrFail('name', 'courses')
    const studentsModule = await Module.findByOrFail('name', 'students')
    
    // Get permissions
    const viewCoursesPermission = await Permission.findByOrFail('name', 'courses.view')
    const editCoursesPermission = await Permission.findByOrFail('name', 'courses.edit')
    const viewStudentsPermission = await Permission.findByOrFail('name', 'students.view')
    
    // Assign permissions
    await RoleModulePermission.createMany([
      {
        role_id: roleId,
        module_id: coursesModule.id,
        permission_id: viewCoursesPermission.id,
        institute_id: instituteId
      },
      {
        role_id: roleId,
        module_id: coursesModule.id,
        permission_id: editCoursesPermission.id,
        institute_id: instituteId
      },
      {
        role_id: roleId,
        module_id: studentsModule.id,
        permission_id: viewStudentsPermission.id,
        institute_id: instituteId
      }
    ])
  }
}