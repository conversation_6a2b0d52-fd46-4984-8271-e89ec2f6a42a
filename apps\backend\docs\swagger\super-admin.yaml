paths:
  # Super Admin Authentication
  /auth/super-admin/login:
    post:
      tags:
        - Super Admin Authentication
      summary: Super Admin Login
      description: Authenticate super admin or LMS admin users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  example: "superadmin123"
      responses:
        200:
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Login successful"
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      tokens:
                        $ref: '#/components/schemas/AuthTokens'
                      permissions:
                        type: array
                        items:
                          type: string
                        example: ["users.create", "institutes.manage", "analytics.view"]
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        422:
          $ref: '#/components/responses/ValidationError'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/super-admin/refresh:
    post:
      tags:
        - Super Admin Authentication
      summary: Refresh Access Token
      description: Refresh the access token using refresh token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refresh_token
              properties:
                refresh_token:
                  type: string
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      responses:
        200:
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Token refreshed successfully"
                  data:
                    type: object
                    properties:
                      tokens:
                        $ref: '#/components/schemas/AuthTokens'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/super-admin/me:
    get:
      tags:
        - Super Admin Authentication
      summary: Get Current User
      description: Get current authenticated super admin user information
      security:
        - bearerAuth: []
      responses:
        200:
          description: User information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/User'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/super-admin/dashboard:
    get:
      tags:
        - Super Admin Authentication
      summary: Get Dashboard Data
      description: Get super admin dashboard statistics and data
      security:
        - bearerAuth: []
      responses:
        200:
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      stats:
                        type: object
                        properties:
                          totalInstitutes:
                            type: integer
                            example: 150
                          totalUsers:
                            type: integer
                            example: 5000
                          totalStudents:
                            type: integer
                            example: 4500
                          totalRevenue:
                            type: number
                            example: 125000.50
                      recentInstitutes:
                        type: array
                        items:
                          $ref: '#/components/schemas/Institute'
                      systemHealth:
                        type: object
                        properties:
                          status:
                            type: string
                            example: "healthy"
                          uptime:
                            type: string
                            example: "99.9%"
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  /auth/super-admin/logout:
    post:
      tags:
        - Super Admin Authentication
      summary: Logout
      description: Logout and invalidate current session
      security:
        - bearerAuth: []
      responses:
        200:
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Logout successful"
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/InternalServerError'

  # Institute Management
  /super-admin/institutes:
    get:
      tags:
        - Super Admin - Institute Management
      summary: List All Institutes
      description: Get paginated list of all institutes with filtering options
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, suspended, pending]
        - name: search
          in: query
          schema:
            type: string
      responses:
        200:
          description: Institutes retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      institutes:
                        type: array
                        items:
                          $ref: '#/components/schemas/Institute'
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        500:
          $ref: '#/components/responses/InternalServerError'

    post:
      tags:
        - Super Admin - Institute Management
      summary: Create New Institute
      description: Create a new institute (super admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - email
                - phone
              properties:
                name:
                  type: string
                  example: "Demo University"
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                phone:
                  type: string
                  example: "+1234567890"
                address:
                  type: string
                  example: "123 Education Street, Learning City"
                website:
                  type: string
                  example: "https://demouniversity.edu"
                type:
                  type: string
                  enum: [university, college, school, training_center, coaching_institute, online_academy, corporate_training]
                  example: "university"
      responses:
        201:
          description: Institute created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Institute created successfully"
                  data:
                    $ref: '#/components/schemas/Institute'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        422:
          $ref: '#/components/responses/ValidationError'
        500:
          $ref: '#/components/responses/InternalServerError'

  /super-admin/institutes/{id}:
    get:
      tags:
        - Super Admin - Institute Management
      summary: Get Institute Details
      description: Get detailed information about a specific institute
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Institute details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Institute'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/InternalServerError'

  # Analytics
  /super-admin/analytics/overview:
    get:
      tags:
        - Super Admin - Analytics
      summary: Get Analytics Overview
      description: Get comprehensive analytics overview for super admin
      security:
        - bearerAuth: []
      responses:
        200:
          description: Analytics overview retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      totalInstitutes:
                        type: integer
                        example: 150
                      totalUsers:
                        type: integer
                        example: 5000
                      totalStudents:
                        type: integer
                        example: 4500
                      totalRevenue:
                        type: number
                        example: 125000.50
                      monthlyGrowth:
                        type: object
                        properties:
                          institutes:
                            type: number
                            example: 12.5
                          users:
                            type: number
                            example: 8.3
                          revenue:
                            type: number
                            example: 15.7
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        500:
          $ref: '#/components/responses/InternalServerError'