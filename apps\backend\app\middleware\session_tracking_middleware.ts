import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import SessionService from '#services/session_service'

export default class SessionTrackingMiddleware {
  private sessionService = new SessionService()

  async handle(ctx: HttpContext, next: NextFn) {
    // Only track for authenticated users
    if (ctx.auth.user) {
      try {
        // Extract session ID from JWT token or request headers
        const sessionId = this.extractSessionId(ctx)
        
        if (sessionId) {
          // Update session activity
          await this.sessionService.updateSessionActivity(sessionId)
        }
      } catch (error) {
        // Log error but don't block the request
        console.error('Session tracking error:', error.message)
      }
    }

    await next()
  }

  /**
   * Extract session ID from request
   * This depends on how you structure your JWT tokens or session management
   */
  private extractSessionId(ctx: HttpContext): string | null {
    // Option 1: From JWT token payload (if you include session ID in token)
    const token = ctx.request.header('authorization')?.replace('Bearer ', '')
    if (token) {
      try {
        // You would decode the JWT and extract session ID
        // const payload = jwt.decode(token)
        // return payload.sessionId
      } catch (error) {
        // Invalid token
      }
    }

    // Option 2: From custom header
    const sessionId = ctx.request.header('x-session-id')
    if (sessionId) {
      return sessionId
    }

    // Option 3: From cookie
    const sessionCookie = ctx.request.cookie('session_id')
    if (sessionCookie) {
      return sessionCookie
    }

    return null
  }
}
