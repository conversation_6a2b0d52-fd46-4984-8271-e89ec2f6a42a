import { BaseSeeder } from '@adonisjs/lucid/seeders';
import Role from '#models/role';
export default class extends BaseSeeder {
    async run() {
        await Role.createMany([
            {
                name: 'super_admin',
                display_name: 'Super Administrator',
                description: 'Full access to all system features',
                is_system_role: true
            },
            {
                name: 'lms_admin',
                display_name: 'LMS Administrator',
                description: 'Manages the entire LMS platform',
                is_system_role: true
            },
            {
                name: 'institute_admin',
                display_name: 'Institute Administrator',
                description: 'Manages an educational institute',
                is_system_role: true
            },
            {
                name: 'institute_staff',
                display_name: 'Institute Staff',
                description: 'Staff member of an educational institute',
                is_system_role: true
            },
            {
                name: 'instructor',
                display_name: 'Instructor',
                description: 'Teaches courses and manages course content',
                is_system_role: true
            },
            {
                name: 'student',
                display_name: 'Student',
                description: 'Enrolled in courses and accesses learning materials',
                is_system_role: true
            }
        ]);
    }
}
//# sourceMappingURL=004_role_seeder.js.map