export default class InstituteMiddleware {
    async handle({ auth, response, params }, next) {
        await auth.authenticate();
        const user = auth.user;
        if (['super_admin', 'lms_admin'].includes(user.role)) {
            return next();
        }
        if (!user.instituteId) {
            return response.status(403).json({
                success: false,
                message: 'Access denied',
                error: 'User not associated with any institute'
            });
        }
        if (params.instituteId && parseInt(params.instituteId) !== user.instituteId) {
            return response.status(403).json({
                success: false,
                message: 'Access denied',
                error: 'Cannot access resources from other institutes'
            });
        }
        return next();
    }
}
//# sourceMappingURL=institute_middleware.js.map