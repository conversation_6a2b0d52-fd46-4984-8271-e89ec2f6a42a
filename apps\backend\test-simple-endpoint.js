async function testSimpleEndpoint() {
  try {
    console.log('🔍 Testing simple endpoint...')
    
    const response = await fetch('http://localhost:3333/')
    const result = await response.json()
    
    console.log('✅ Root endpoint response:', result)
    
    // Test health endpoint
    const healthResponse = await fetch('http://localhost:3333/api/health')
    const healthResult = await healthResponse.json()
    
    console.log('✅ Health endpoint response:', healthResult)
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testSimpleEndpoint()
