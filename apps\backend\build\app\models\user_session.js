var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm';
import User from './user.js';
export default class UserSession extends BaseModel {
    get isActive() {
        return this.status === 'active' && this.expiresAt > DateTime.now();
    }
    get isExpired() {
        return this.expiresAt <= DateTime.now();
    }
    get isLoggedOut() {
        return this.status === 'logged_out' || this.logoutAt !== null;
    }
    get isRevoked() {
        return this.status === 'revoked';
    }
    get sessionDuration() {
        const endTime = this.logoutAt || this.lastActivityAt;
        return endTime.diff(this.loginAt).as('minutes');
    }
    get deviceInfo() {
        return {
            type: this.deviceType,
            name: this.deviceName,
            model: this.deviceModel,
            vendor: this.deviceVendor,
            os: this.operatingSystem,
            osVersion: this.osVersion,
            isMobile: this.isMobile,
        };
    }
    get browserInfo() {
        return {
            name: this.browserName,
            version: this.browserVersion,
            engine: this.browserEngine,
            userAgent: this.userAgent,
        };
    }
    get locationInfo() {
        return {
            ip: this.ipAddress,
            country: this.country,
            region: this.region,
            city: this.city,
            timezone: this.timezone,
            isp: this.isp,
        };
    }
    get securityInfo() {
        return {
            isTrusted: this.isTrustedDevice,
            isSuspicious: this.isSuspicious,
            isBot: this.isBot,
            notes: this.securityNotes,
        };
    }
    static async createSession(data) {
        return await UserSession.create(data);
    }
    static async findActiveSession(sessionId) {
        return await UserSession.query()
            .where('sessionId', sessionId)
            .where('status', 'active')
            .where('expiresAt', '>', DateTime.now().toSQL())
            .first();
    }
    static async findUserActiveSessions(userId) {
        return await UserSession.query()
            .where('userId', userId)
            .where('status', 'active')
            .where('expiresAt', '>', DateTime.now().toSQL())
            .orderBy('lastActivityAt', 'desc');
    }
    static async revokeSession(sessionId) {
        return await UserSession.query()
            .where('sessionId', sessionId)
            .update({
            status: 'revoked',
            logoutAt: DateTime.now(),
            updatedAt: DateTime.now(),
        });
    }
    static async revokeAllUserSessions(userId, exceptSessionId) {
        const query = UserSession.query()
            .where('userId', userId)
            .where('status', 'active');
        if (exceptSessionId) {
            query.whereNot('sessionId', exceptSessionId);
        }
        return await query.update({
            status: 'revoked',
            logoutAt: DateTime.now(),
            updatedAt: DateTime.now(),
        });
    }
    static async cleanupExpiredSessions() {
        return await UserSession.query()
            .where('expiresAt', '<=', DateTime.now().toSQL())
            .where('status', 'active')
            .update({
            status: 'expired',
            updatedAt: DateTime.now(),
        });
    }
    async updateActivity() {
        this.lastActivityAt = DateTime.now();
        await this.save();
    }
    async markAsLoggedOut() {
        this.status = 'logged_out';
        this.logoutAt = DateTime.now();
        await this.save();
    }
    async markAsRevoked(reason) {
        this.status = 'revoked';
        this.logoutAt = DateTime.now();
        if (reason) {
            this.securityNotes = reason;
        }
        await this.save();
    }
    async markAsSuspicious(reason) {
        this.isSuspicious = true;
        this.securityNotes = reason;
        await this.save();
    }
    async markAsTrusted() {
        this.isTrustedDevice = true;
        await this.save();
    }
    serialize() {
        return {
            id: this.id,
            sessionId: this.sessionId,
            tokenType: this.tokenType,
            device: this.deviceInfo,
            browser: this.browserInfo,
            location: this.locationInfo,
            security: this.securityInfo,
            loginAt: this.loginAt,
            lastActivityAt: this.lastActivityAt,
            expiresAt: this.expiresAt,
            logoutAt: this.logoutAt,
            status: this.status,
            isCurrentSession: this.isCurrentSession,
            sessionDuration: this.sessionDuration,
            referrer: this.referrer,
            landingPage: this.landingPage,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    serializeMinimal() {
        return {
            id: this.id,
            deviceType: this.deviceType,
            browserName: this.browserName,
            location: `${this.city || 'Unknown'}, ${this.country || 'Unknown'}`,
            loginAt: this.loginAt,
            lastActivityAt: this.lastActivityAt,
            isCurrentSession: this.isCurrentSession,
            status: this.status,
        };
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], UserSession.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", Number)
], UserSession.prototype, "userId", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], UserSession.prototype, "sessionId", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], UserSession.prototype, "tokenId", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], UserSession.prototype, "tokenType", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "deviceType", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "deviceName", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "deviceModel", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "deviceVendor", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "operatingSystem", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "osVersion", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "browserName", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "browserVersion", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "browserEngine", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "userAgent", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], UserSession.prototype, "ipAddress", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "country", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "region", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "city", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "timezone", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "isp", void 0);
__decorate([
    column.dateTime(),
    __metadata("design:type", DateTime)
], UserSession.prototype, "loginAt", void 0);
__decorate([
    column.dateTime(),
    __metadata("design:type", DateTime)
], UserSession.prototype, "lastActivityAt", void 0);
__decorate([
    column.dateTime(),
    __metadata("design:type", DateTime)
], UserSession.prototype, "expiresAt", void 0);
__decorate([
    column.dateTime(),
    __metadata("design:type", Object)
], UserSession.prototype, "logoutAt", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], UserSession.prototype, "status", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], UserSession.prototype, "isCurrentSession", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], UserSession.prototype, "isTrustedDevice", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], UserSession.prototype, "isMobile", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], UserSession.prototype, "isBot", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], UserSession.prototype, "isSuspicious", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "securityNotes", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "metadata", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "referrer", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], UserSession.prototype, "landingPage", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], UserSession.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], UserSession.prototype, "updatedAt", void 0);
__decorate([
    belongsTo(() => User),
    __metadata("design:type", Object)
], UserSession.prototype, "user", void 0);
//# sourceMappingURL=user_session.js.map