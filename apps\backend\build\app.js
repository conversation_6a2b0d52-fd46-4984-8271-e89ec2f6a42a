import 'reflect-metadata';
import { Ignitor } from '@adonisjs/core';
const APP_ROOT = new URL('./', import.meta.url);
const IMPORTER = (filePath) => {
    if (filePath.startsWith('./') || filePath.startsWith('../')) {
        return import(new URL(filePath, APP_ROOT).href);
    }
    return import(filePath);
};
const options = {
    importer: IMPORTER,
};
export const loadApp = () => {
    return new Ignitor(APP_ROOT, options).createApp('web');
};
export const startApp = () => {
    return new Ignitor(APP_ROOT, options).httpServer().start();
};
//# sourceMappingURL=app.js.map