var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import hash from '@adonisjs/core/services/hash';
import { compose } from '@adonisjs/core/helpers';
import { BaseModel, column, belongsTo, hasMany, beforeSave } from '@adonisjs/lucid/orm';
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid';
import Institute from './institute.js';
import Branch from './branch.js';
import Enrollment from './enrollment.js';
import UserRole from './user_role.js';
import UserSession from './user_session.js';
const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
    uids: ['email', 'student_id'],
    passwordColumnName: 'password',
});
export default class User extends compose(BaseModel, AuthFinder) {
    get fullName() {
        return `${this.firstName} ${this.lastName}`;
    }
    get initials() {
        return `${this.firstName.charAt(0)}${this.lastName.charAt(0)}`.toUpperCase();
    }
    get isActive() {
        return this.status === 'active';
    }
    get isSuperAdmin() {
        return this.role === 'super_admin';
    }
    get isLmsAdmin() {
        return this.role === 'lms_admin';
    }
    get isInstituteAdmin() {
        return this.role === 'institute_admin';
    }
    get isInstituteStaff() {
        return this.role === 'institute_staff';
    }
    get isStudent() {
        return this.role === 'student';
    }
    static async hashPassword(user) {
        if (user.$dirty.password) {
            user.password = await hash.make(user.password);
        }
    }
    async verifyPassword(plainPassword) {
        return await hash.verify(this.password, plainPassword);
    }
    serialize() {
        return {
            id: this.id,
            email: this.email,
            studentId: this.studentId,
            firstName: this.firstName,
            lastName: this.lastName,
            fullName: this.fullName,
            initials: this.initials,
            phone: this.phone,
            avatar: this.avatar,
            role: this.role,
            status: this.status,
            instituteId: this.instituteId,
            branchId: this.branchId,
            emailVerifiedAt: this.emailVerifiedAt,
            lastLoginAt: this.lastLoginAt,
            twoFactorEnabled: this.twoFactorEnabled,
            preferences: this.preferences,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], User.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "email", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "studentId", void 0);
__decorate([
    column({ serializeAs: null }),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], User.prototype, "firstName", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], User.prototype, "lastName", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "phone", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "avatar", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], User.prototype, "role", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], User.prototype, "status", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "instituteId", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "branchId", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "emailVerifiedAt", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "lastLoginAt", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "lastLoginIp", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], User.prototype, "twoFactorEnabled", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "twoFactorSecret", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], User.prototype, "preferences", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], User.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], User.prototype, "updatedAt", void 0);
__decorate([
    column.dateTime(),
    __metadata("design:type", Object)
], User.prototype, "deletedAt", void 0);
__decorate([
    belongsTo(() => Institute),
    __metadata("design:type", Object)
], User.prototype, "institute", void 0);
__decorate([
    belongsTo(() => Branch),
    __metadata("design:type", Object)
], User.prototype, "branch", void 0);
__decorate([
    hasMany(() => Enrollment),
    __metadata("design:type", Object)
], User.prototype, "enrollments", void 0);
__decorate([
    hasMany(() => UserRole),
    __metadata("design:type", Object)
], User.prototype, "userRoles", void 0);
__decorate([
    hasMany(() => UserSession),
    __metadata("design:type", Object)
], User.prototype, "sessions", void 0);
__decorate([
    beforeSave(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [User]),
    __metadata("design:returntype", Promise)
], User, "hashPassword", null);
//# sourceMappingURL=user.js.map